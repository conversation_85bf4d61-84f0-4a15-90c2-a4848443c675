{"auth": {"authorization": "Authorization", "emailOrNickname": "Email address / nickname", "password": "Password"}, "dashboard": {"12monthsResults": "Result for the last 12 months", "abiForTheLastMonth": "ABI for the last month", "abiTop": "You are in the top {{value}}% of the ABI", "achievements": "Achievements", "all": "All", "BERTooltip": "Shows how effectively you use the allocated funds. It is calculated as the ratio of the load for the day to the maximum balance of the project. The higher the KEB, the better.", "continueLearning": "Continue learning", "currentSection": "Current section", "editAvatar": "Edit avatar", "finance": "Finance", "hoursOfEducation": "Hours of education", "last100k": "Last 100k hands", "lastMonth": "Last month", "league": "League", "leagueTop": "You are in the top {{value}}% of the league", "needToWorkOn": "Need to work on", "overallProgress": "Overall progress", "playersPath": "Player's Path", "selectRating": "Select rating", "sendOverage": "Need to send", "studiedForHours": "Studied for hours", "tasksCompleted": "Tasks completed", "timeInTheProject": "Time in the project", "toEducation": "To education", "top3": "Top 3 Cashes", "indicator_one": "{{count}} indicator", "indicator_few": "{{count}} indicators", "indicator_many": "{{count}} indicators"}, "education": {"allTasks": "All tasks", "baseCheckNotEnoughData": "Evaluation will appear after you have played {{reqTourneysCount}} tourneys. There is not enough data for analysis at the moment", "baseCheckPrevLevel": "Intermediate evaluation based on the results of {{prevLevelTourneysCount}} tourneys from the previous level. As soon as {{reqTourneysCount}} tourneys are played on the current level, we will update the evaluation. Continue to play and complete the tasks", "baseCheckPrevLevelNotEnoughHands": "Intermediate evaluation based on the results of {{prevLevelTourneysCount}} tourneys from the previous level. You have completed all the tasks and played the distance on the current level, but to evaluate the base, you need a minimum of {{minReqHands}} hands (currently {{currentHandsCount}}). As soon as this condition is met, the evaluation for the current level will appear instead of the previous one", "baseCheckCurrentLevelIncomplete": "Intermediate evaluation based on the results of {{tourneysCount}} tourneys on the level. The final evaluation will be available after playing {{reqTourneysCount}} tourneys and completing 100% of the tasks on the level. Continue to play and learn", "baseCheckCurrentLevelTourneysIncomplete": "Intermediate evaluation based on the results of {{tourneysCount}} tourneys on the level. The final evaluation will be available after playing {{reqTourneysCount}} tourneys on the level. Continue to play and learn", "baseCheckCurrentLevelTasksIncomplete": "Intermediate evaluation based on the results of {{tourneysCount}} tourneys on the level. The final evaluation will be available after completing 100% of the tasks on the level. Continue to play and learn", "baseCheckLevelUp": "On {{date}} based on the results of processing {{tourneysCount}} tourneys. The threshold of 50% is completed - you are ready for the next level", "baseCheckLevelUpNotEnough": "On {{date}} based on the results of processing {{tourneysCount}} tourneys. To go to the next level, the base evaluation must be 50% or higher. Continue to play, fix errors and improve your game", "baseCheckLevel": "Base check, %", "baseCheckUnavailable": "Base check will be available after completing the previous progress bars", "check": "Check", "clickToCopyYourNickname": "Click to copy your nickname", "completeChapter": "Complete the chapter", "completedTasks": "Tasks completed", "completeLesson": "Complete the lesson", "continue": "Continue", "continueTasks": "Continue tasks", "distanceLevel": "Distance, MTT", "downloadPdf": "Download pdf", "education": "Education", "from": "from", "goThroughTheChapter": "Go through the chapter", "goToTask": "Go to task", "hoursOfTraining": "Hours of training", "levelUpMessage": "Play tournaments and complete lessons to level up", "minutesOfTraining": "minutes of training", "next": "Next", "nextLesson": "Next lesson", "noCoursesAvailable": "No courses available", "passed": "Passed", "prev": "Back", "prevLesson": "Previous lesson", "rateTaskDifficulty": "Rate the difficulty of the task", "rateTaskInterestingness": "Rate the interestingness of the task", "repeat": "Repeat", "requestVerification": "Request verification", "seeMore": "See more", "startLearning": "Start learning", "step": "Step", "stepsTaken": "Steps taken", "successfulTask": "successful task", "taskCompleted": "Task accomplished!", "taskProgress": "Progress on the task", "tasksCompleted": "Tasks completed", "tasksCompletionInProfile": "Completion of tasks will be indicated in your profile", "tasksLevel": "Tasks, %", "hours_one": "hour", "hours_other": "hours", "minutes_one": "minute", "minutes_other": "minutes", "tasks_one": "task", "tasks_other": "tasks"}, "finances": {"balanceHistory": "Balance history", "packageOpened": "Package opened", "packageOnCheckout": "Package on checkout", "packageClosed": "Package closed", "packageProfit": "Package profit"}, "global": {"$/tour": "$/tour", "2fa": "Two Factor Authentication", "abi": "ABI", "aboutTourney": "About tourney", "add": "Add", "afs": "AFS", "all": "All", "allFilters": "All filters", "allTourneys": "All tourneys", "answer": "Answer", "avatar": "Avatar", "bad": "Bad", "BER": "BER", "BER_full": "Balance efficiency ratio", "buyin": "Buy-in", "cancel": "Cancel", "change": "Change", "changeTheme": "Change theme", "comment": "Comment", "confirm": "Confirm", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dataFetchingProblem": "Temporary data fetching problem", "date": "Date", "deleted": "Deleted", "didntPlay": "Didn't play", "dist_s": "Dist", "distance": "Distance", "done": "Done", "endDate": "End date", "enterResults": "Enter", "ev": "EV", "excellent": "Excellent", "finish": "Finish", "finished": "Finished", "fishes": "Fishes", "gameStats": "Game statistics", "good": "Good", "hide": "<PERSON>de", "k": "k", "ko": "KO", "legend": "Legend", "load": "Load", "lvl": "Level", "month": "Month", "name": "Name", "noAccess": "No access", "noAccessMessage": "You do not have access to this resource. Please contact the administrator", "noProblemsFound": "No problems found", "notAllowed": "Not allowed", "notifications": "Notifications", "numberOfRooms": "Number of rooms", "numberOfTournaments": "Number of tournaments", "off": "Off", "ok": "Ok", "on": "On", "palette": "Palette", "password": "Password", "period": "Period", "perYear": "per year", "play": "Play", "played": "Played", "processing": "Processing", "profile": "Profile", "progress": "Progress", "rank": "Rank", "reentry": "Re-entry", "rejected": "Rejected", "remove": "Remove", "request": "Request", "requiredField": "This field is required", "roi": "ROI", "room": "Room", "rooms": "Rooms", "save": "Save", "scale": "Scale", "security": "Security", "send": "Send", "sessionDuration": "Session duration", "show": "Show", "somethingWentWrong": "Something went wrong", "start": "Start", "startDate": "Start date", "status": "Status", "summary": "Summary", "time": "Time", "tourney": "Tourney", "tourneyType": "Tourney type", "tryLater": "Try again later", "type": "Type", "UILanguage": "Interface Language", "unknownStatus": "Unknown status", "updated": "Updated", "updateIn": "Update in", "winrate": "Winrate", "written": "Written", "yourStats": "Your statistics"}, "guides": {"abi": "ABI", "active-rooms": "Active rooms", "afs": "AFS", "bad-turbo": "Bad turbo & hyper tournaments", "distance": "Distance", "distance-select": "Distance", "early-early-mid": "Early-Early/Mid", "ev-by-level": "EV by tournament difficulty", "ev-by-rooms": "EV by rooms", "full-ring": "Fullring turbo PKO", "month-session": "Number of your sessions per month", "mtt-session": "MTT Session", "profit": "Profit", "reentry": "Reentry", "reg-speed": "Regspeed PKO", "roi": "ROI", "select-rating": "Select rating", "top-3-wins": "Top 3 wins", "tournaments": "Memo"}, "navMenu": {"courses": "FF education", "finances": "Finances", "gameStats": "Game statistics", "main": "Main", "select": "Select", "settings": "Settings", "tournaments": "Tournaments"}, "referal": {"commonStatistic": {"most_friend": "Most active inviter", "people_join": "Joined the fund this month", "title": "General statistics"}, "dashboard": {"button": "Invite a <PERSON>", "description": "Get nice rewards for every friend you refer", "title": "Invite Friends"}, "faq": {"1_answer": "The bonus amount depends on the average ABI of the invited friend. The amount ranges from $100 to $3500. Here is the exact breakdown:\n\nBeginners:\nPlayer who signed a contract with the fund after completing the FFStart course - $100\n\nExperienced players:\nABI 1-5 — $150\nABI 5-10 — $200\nABI 10-15 — $300\nABI 15-20 — $500\nABI 20-30 — $700\nABI 30-40 — $1000\nABI 40-50 — $1500\nABI 50-60 — $2000\nABI 60-100 — $2500\nABI 100+ — $3500\n\nTo receive a payment, a friend must join the fund through your link or inform that he is from you, verbally/in writing, sign a contract, pass the trial period and not be excluded after it expires. They also must not leave voluntarily at the end of the trial period (usually one month, but there may be nuances). After meeting all conditions, we will credit the bonus between the 1st and 10th of the following calendar month.", "1_question": "What payout will I receive and under what conditions?", "2_answer": "Invite those who are interested in or already play online poker and are motivated to grow their career. You probably know people in other funds who are considering switching, or amateurs who play on their own. We believe you can explain the main advantages of FF. If you lack knowledge, once your friend registers, our admins will help fill in the gaps. The main thing is that your recommendation should be sincere and based on personal experience. Context also matters — when relevant discussions come up in chats or private conversations, you can successfully share your link.\nDon’t hide the fact that this is a referral program — many people are already familiar with this concept from banks, apps, etc. Also, don’t hide your interest in having your friend join the fund. This way, you’ll be in the same community, and you’ll also get a reward. ", "2_question": "How to choose a friend to recommend?", "3_answer": "Tell them that you’re recommending a great poker fund where they will get support, top-quality training, unlimited bankroll, and a community of like-minded people hungry for success.\nAchieving results is easier in a professional team than alone — you can use this as an argument.\nWe have a well-structured learning process and strong IT solutions. The fund has existed for more than 10 years and has never gone bankrupt, which proves we know how to help players win. ", "3_question": "What should I tell them?", "4_answer": "Copy your personal link and send it to your friend. Explain that it is important to apply specifically through this link, otherwise the system will not record your participation in bringing the player. ", "4_question": "How do I share my personal link so the bonus is credited to me?", "5_answer": "Your friend must follow your referral link, sign a contract with the fund, pass the trial period, not terminate it during the trial period (usually it lasts a month, but sometimes there are nuances), and not be kicked out.\n\nIf these conditions are met, you will receive a payout between the 1st and 10th of the following calendar month. For example, if your friend registered on August 13, signed a contract on August 15, and started playing, then on September 15 he has not quit or been kicked out. In that case, you’ll receive your payout between October 1 and 10.\nIf a person was determined in FF.Start and after completing it signed a contract with the fund, for example, on August 15, then you will receive $100 from October 1 to 10.\n", "5_question": "What needs to be done for the bonus to be credited?", "title": "Frequently asked questions"}, "linkBlock": {"copy": "Copied to clipboard!", "description": "Share your invitation link with friends", "link_title": "Invitation link for friends", "share": "Share", "title": "Invite friends, get bonuses"}, "myStatistic": {"friends_contract": "Friends signed a contract", "friends_invites": "Friends applied", "money_received": "Payouts received", "title": "My statistics"}, "stepsBlock": {"block_title": "How to get a reward?", "step_1_description": "Copy your personal link or send the QR code to a friend to join the fund", "step_1_title": "Invite a friend", "step_2_description": "Your friend must sign a contract with the fund and not leave during the trial period. They also must not be kicked out during this time", "step_2_title": "Wait for the conditions to be met", "step_3_description": "Referral bonuses are credited between the 1st and 10th of the following month, after all conditions are met", "step_3_title": "Receive your payout"}}, "select": {"abi": "ABI", "activeRooms": "Number of active rooms", "afs": "AFS", "badTurboHyperTourney": "Bad turbo & hyper tournaments", "country": "Country", "distance": "Distance", "earlyEarlyMid": "Early departures", "fullringRegspeedFreezout": "Fullring Regspeed Freezout", "fullringTurboPko": "Fullring turbo PKO", "mttSession": "Tournaments per session", "ratingSelect": "Select rating", "reentry": "Re-entry", "regspeedPko": "Regspeed PKO", "sessionsCountPerMonth": "Number of sessions per month", "shorthandedRgspeedFreezout": "Shorthanded Regsp<PERSON> Freezout", "shorthandedTurboHyperPko": "Shorthanded turbo & hyper PKO"}, "settings": {"changeAvatar": "You can change your profile photo", "changePassword": "Change password", "changePasswordForSecurity": "Change your current password to enhance security", "createNewPassword": "Create new password", "disable2fa": "Are you sure you want to disable two-factor authentication?", "enable2faForSecurity": "Enable an extra layer of protection for your account", "get2faCode": "Verification with Google Authenticator app. Enter 6-digit code generated by the app for authentication", "newPassword": "New password", "notificationsSettings": "Notifications settings", "oldPassword": "Old password", "passwordRequirements": "Password must contain at least 8 characters, numbers and special characters (! “ # $ % () *)", "passwordsDoNotMatch": "Passwords do not match", "repeatPassword": "Repeat password", "scanQRCode": "Scan the QR code or add the code manually to add the app to Google Authenticator", "selectNotifications": "Select the incoming notifications you would like to receive via Discord", "selectUILanguage": "Choose the language you want to use for the interface", "setupNotifications": "Set up the notifications you want to receive"}, "statistics": {"acceptableWR": "With potential variance the win rate should be no lower than", "allHands": "All hands", "atAbi": "At your ABI", "average": "Average", "avgByLeague": "Average by league", "avgByRank": "Average by rank", "awaitingInspection": "Awaiting inspection", "awaitingVerification": "Awaiting verification", "baseEvaluation": "Base Evaluation", "bbWinrate": "bb/100, winrate", "baseQuality": "base quality", "blindLevel": "Blind level", "blockWillOpenAfterStudying": "The block will open after studying this topic", "borderline": "borderline", "borderlineIndicator": "Borderline", "boundaryIndicators": "Boundary indicators", "boundaryIndicatorsText": "Require extra attention. Keep them under control and focus on the necessary spots during each session.\nA small number of marginal indicators are allowed. And it is absolutely normal for them to move from marginal to excellent and back. But it is not acceptable for marginal indicators to move to terrible.", "buildAnalytics": "Build analytics", "buildStatistics": "Build statistics", "byBlinds": "By level of blinds", "byDifficulty": "By difficulty of tournaments", "byHUD": "By HUD", "byNumberOfHands": "By number of hands", "byPeriod": "By period", "byPosition": "By position", "byPositions": "By positions", "byStackSize": "By stack size", "byTableSize": "By table size", "byTournamentDifficulty": "By tournament difficulty", "byTourneyType": "By tourney type", "comparisonOfStatistics": "Comparison of statistics", "colorTheStats": "Color the stats", "category": "Category", "charactersLeftToWrite": "Characters left to write", "correctTheIndicator": "Correct the indicator", "currentIndicator": "Current indicator", "comment": "Comment", "comments": "Comments", "correct": "Correct", "correctionOfTheIndicator": "Correction of the indicator", "congratulationsYouCompletedTasks": "Congratulations, you have completed all the tasks!", "dateOfTheProblem": "Date of the problem", "describeTheProblem": "Describe what actions were taken to fix the problem. Comment minimum 300 characters.", "done": "Done", "duplicateFilters": "Duplicate\nfilters", "eVByRooms": "EV by rooms", "excellent": "excellent", "excellentIndicator": "Excellent", "excellentIndicators": "Excellent indicators", "excellentIndicatorsText": "Try to make all your indicators fall into the excellent category. But remember that each stat must be filled qualitatively, in accordance with the recommendations given by the coach.\nIt is strictly forbidden to open 72o from UTG in order to wind up an excellent indicator!", "execute": "Execute", "gotAcquainted": "Got acquainted", "handCount": "Hand count", "handsSelection": "hand selection", "howToWorkWithStatistics": "How to work with statistics", "indicator": "Indicator", "indicatorHasBeenMovedToStatus": "Indicator “{{indicator}}” has been moved\nto status “{{status}}”", "instruction": "Instruction", "instructionsForCorrectingTheIndicator": "Instructions for correcting the indicator “{{indicator}}”", "inWork": "In work", "kHandsEach": "{{count}}k hands each", "kUntilNextCheck": "There are {{value}}k left until the next check", "lastK": "Last {{count}}k", "lastKHands": "Last {{value}}k hands", "lastKInTheRoom": "Last {{count}}k in the room", "lastSystemCheck": "Last system check", "look": "Look", "markTasksAfterWork": "Mark tasks only after you have worked through the recommendations in detail", "myIndicator": "My indicator", "myValue": "My value", "myWinrate": "My winrate", "new": "New", "noProblematicIndicators": "There are no problematic indicators", "notEnoughDataForEvaluation": "Not enough data for evaluation", "notEnoughSample": "Insufficient sample.\nChoose a longer period", "now": "Now", "numberOfAssessedIndicators": "Number of assessed\nindicators", "previousIndicator": "Previous indicator", "problematicIndicator": "Problematic indicator", "problematicIndicators": "Problematic indicators", "progressOfImplementation": "Progress of implementation", "recommended": "Recommended", "resetAllFilters": "Reset all\nfilters", "roomsWithHUD": "Rooms with HUD", "roomsWithoutHUD": "Rooms without HUD", "sample": "<PERSON><PERSON>", "sampleOfKHands": "Sample of {{value}}k hands", "smallSample": "Small sample", "square": "Square", "stackSize": "Stack size", "statusOfTheProblemChanged": "The status of the problem has been changed. After checking the system,\nif the problem is solved correctly, it will be removed from the list", "systemCheck": "System check", "terrible": "terrible", "terribleIndicator": "Terrible", "terribleIndicators": "Terrible indicators", "terribleIndicatorsText": "Don't let your indicators fall into the category of terrible.\nIf such an indicator appears, we issue a task for correction.\nMake every effort to correct the indicator.", "theProblemHasBeenWorkedOut": "The problem has been worked out", "total": "Conclusion", "totalIndicatorsProcessed": "Total indicators processed", "toTheRecommendations": "To the recommendations", "unrated": "Unrated", "was": "Was", "watchedRecommendedVideos": "Watched recommended videos", "wellDeveloped": "Well-developed", "winrateInSections": "Winrate in sections", "worked": "Worked", "workingThroughTheProblem": "Working through the problem", "workIsRequired": "Work is required"}, "time": {"runTime": "Running", "startIn": "In", "hours_one": "{{count}} hour", "hours_other": "{{count}} hours", "minutes_one": "{{count}} minute", "minutes_other": "{{count}} minutes"}, "tournaments": {"addTemplate": "Add template", "alwaysInPackage": "Always in package", "editTemplate": "Edit template", "enterResults": "Enter results from self", "enterTemplateName": "Enter template name", "foundTourneys": "Found tourneys", "freezeOut": "Freeze-out", "freezeOutRebuy": "Freeze-out Rebuy", "fromSelf": "From self", "hidden": "Hidden", "intoThePackage": "Into the package", "invalidTimeFormat": "Invalid time format", "koRebuy": "KO Rebuy", "limitsForRooms": "Limits for rooms that are not in the main table", "manualInputTourney": "Manual input tourney", "minBuyin": "<PERSON>in", "partOfThePackage": "Part of the package", "past": "Past", "rebuyDefinition": "Rebuy is a tournament in which you can immediately buy additional chips after the start. Plus there is an add-on - the ability to buy chips during the break at an improved rate. Other tournaments, for example, where you can leave and make a reentry - these are tournaments with reentries and have nothing to do with rebuys", "regspeed6max": "<PERSON><PERSON><PERSON> 6-max", "regspeed6maxDefinition": "6 or less people at the table and levels of 7 minutes or more", "regspeedFullring": "Reg<PERSON><PERSON> Fullring", "regspeedFullringDefinition": "7 or more people at the table and levels of 7 minutes or more", "requestAdmission": "Request admission", "series": "Regulars/Series", "setupLegend": "Setup legend", "setValuesForTemplate": "Specify the values that will be applied when selecting a template", "showTournamentsList": "Show tournaments list", "templateName": "Template name", "to": "to", "tournamentAdmissions": "Tournament admissions", "tourneyName": "Название турнира", "transactionWillBeRecorded": "Transaction will be recorded automatically in the \"Finances\" section", "turbo6max": "Turbo 6-max", "turbo6maxDefinition": "6 or less people at the table and levels of 4-6 minutes", "turboFullring": "Turbo Fullring", "turboFullringDefinition": "7 or more people at the table and levels of 4-6 minutes", "willNotPlay": "Will not play", "willPlay": "<PERSON> play"}, "all": "All", "fetchingError": "There was an error loading data. Try refreshing the page or try again later.", "day_other": "days", "day_one": "day", "day_few": "days", "day_many": "days", "day_short": "d", "hand_other": "hands", "hand_one": "hand", "hand_few": "hands", "hand_many": "hands", "hand_short": "hnd", "hour_other": "hours", "hour_one": "hour", "hour_few": "hours", "hour_many": "hours", "hour_short": "h", "min_other": "minutes", "min_one": "minute", "min_few": "minutes", "min_many": "minutes", "min_short": "min", "month_other": "months", "month_one": "month", "month_few": "months", "month_many": "months", "month_short": "mon", "selected": "selected", "year_other": "years", "year_one": "year", "year_few": "years", "year_many": "years", "year_short": "y"}