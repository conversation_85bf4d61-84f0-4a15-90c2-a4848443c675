{"auth": {"authorization": "Авторизация", "emailOrNickname": "Адрес эл. почты / никнейм", "password": "Пароль"}, "dashboard": {"12monthsResults": "Результат за последние 12 месяцев", "abiForTheLastMonth": "ABI за последний месяц", "abiTop": "Входишь в ТОП {{value}}% по ABI", "achievements": "Достижения", "all": "Все", "BERTooltip": "Показывает, насколько эффективно вы используете выделенные средства. Считается как отношение загрузки за день к максимальному остатку средств проекта. Чем выше КЭБ, тем лучше.", "continueLearning": "Продолжить обучение", "currentSection": "Текущий раздел", "editAvatar": "Редактировать аватар", "finance": "Финан<PERSON>ы", "hoursOfEducation": "Часы обучения", "last100k": "За последние 100к рук", "lastMonth": "За последний месяц", "league": "Лига", "leagueTop": "Входишь в ТОП {{value}}% по лиге", "needToWorkOn": "Требуется проработать", "overallProgress": "Общий прогресс", "playersPath": "Путь игрока", "selectRating": "Оценка селекта", "sendOverage": "Необходимо разослать", "studiedForHours": "Обуча<PERSON><PERSON>я часов", "tasksCompleted": "Заданий выполнено", "timeInTheProject": "Время в проекте", "toEducation": "К обучению", "top3": "Топ-3 заноса", "indicator_one": "{{count}} показатель", "indicator_few": "{{count}} показателя", "indicator_many": "{{count}} показателей"}, "education": {"allTasks": "Все задания", "baseCheckNotEnoughData": "Оценка появится после того, как вы сыграете {{reqTourneysCount}} турниров. До этого момента данных для анализа не достаточно", "baseCheckPrevLevel": "Промежуточная оценка по результатам {{prevLevelTourneysCount}} турниров с предыдущего уровня. Как только на текущем уровне будет наиграно {{reqTourneysCount}} турниров, мы обновим оценку. Продолжайте играть и выполнять задания", "baseCheckPrevLevelNotEnoughHands": "Промежуточная оценка по {{prevLevelTourneysCount}} турнирам прошлого уровня. Ты выполнил все задания и наиграл дистанцию на текущем уровне, но для оценки базы нужно минимум {{minReqHands}} рук (сейчас {{currentHandsCount}}). Как только это условие выполнится, появится оценка по текущему уровню вместо прошлой", "baseCheckCurrentLevelIncomplete": "Промежуточная оценка по результатам {{tourneysCount}} турниров на уровне. Итоговую оценку вы получите после отыгрыша {{reqTourneysCount}} турниров и выполнения 100% заданий на уровне. Продолжайте играть и обучаться", "baseCheckCurrentLevelTourneysIncomplete": "Промежуточная оценка по результатам {{tourneysCount}} турниров на уровне. Итоговую оценку вы получите после отыгрыша {{reqTourneysCount}} турниров на уровне. Продолжайте играть и обучаться", "baseCheckCurrentLevelTasksIncomplete": "Промежуточная оценка по результатам {{tourneysCount}} турниров на уровне. Итоговую оценку вы получите после выполнения 100% заданий на уровне. Продолжайте играть и обучаться", "baseCheckLevelUp": "На {{date}} по результатам обработки {{tourneysCount}} турниров. Порог 50% выполнен - вы готовы к следующему уровню", "baseCheckLevelUpNotEnough": "На {{date}} по результатам обработки {{tourneysCount}} турниров. Для перехода на следующий уровень оценка базы должна быть 50% или лучше. Продолжайте играть, разбирайте ошибки и улучшайте игру", "baseCheckLevel": "Оценка базы, %", "baseCheckUnavailable": "Оценка будет доступна после завершения предыдущих прогресс-баров", "check": "Проверить", "clickToCopyYourNickname": "Кликни, чтобы скопировать свой ник", "completeChapter": "Завершить раздел", "completedTasks": "Выполнено заданий", "completeLesson": "Завер<PERSON>ить урок", "continue": "Продолжить", "continueTasks": "Продолжить задания", "distanceLevel": "Дистанция, МТТ", "downloadPdf": "Скачать pdf", "education": "Обучение", "from": "из", "goThroughTheChapter": "Пройдите раздел", "goToTask": "Перейти к заданию", "hoursOfTraining": "Часов обучения", "levelUpMessage": "Играй турниры и проходи уроки, чтобы перейти на следующий уровень", "minutesOfTraining": "минут обучения", "next": "Далее", "nextLesson": "Следующий урок", "noCoursesAvailable": "Нет доступных курсов", "passed": "Пройдено", "prev": "Назад", "prevLesson": "Предыдущий урок", "rateTaskDifficulty": "Оцените сложность задания", "rateTaskInterestingness": "Оцените интересность задания", "repeat": "Повторить", "requestVerification": "Запросить проверку", "seeMore": "Посмотреть еще", "startLearning": "Начать обучение", "step": "<PERSON>аг", "stepsTaken": "Шагов пройдено", "successfulTask": "успешное задание", "taskCompleted": "Задание выполнено!", "taskProgress": "Прогресс по заданию", "tasksCompleted": "Заданий пройдено", "tasksCompletionInProfile": "Выполнение заданий будет указано в твоём профиле", "tasksLevel": "Задания, %", "hours_one": "час", "hours_few": "часа", "hours_many": "<PERSON><PERSON><PERSON><PERSON>", "minutes_one": "минута", "minutes_few": "минуты", "minutes_many": "минут", "tasks_one": "задание", "tasks_few": "задания", "tasks_many": "заданий"}, "finances": {"balanceHistory": "История заполнения балансов", "packageClosed": "Пакет закрыт", "packageOnCheckout": "Пакет на расчёте", "packageOpened": "Пакет открыт", "packageProfit": "Профит пакета"}, "global": {"$/tour": "$/тур", "2fa": "Двухфакторная аутентификация", "abi": "АБИ", "aboutTourney": "О турнире", "add": "Добавить", "afs": "АФС", "all": "Все", "allFilters": "Все фильтры", "allTourneys": "Все турниры", "answer": "Ответ", "avatar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bad": "Плохо", "BER": "КЭБ", "BER_full": "Коэффициент эффективности балансов", "buyin": "Бай-ин", "cancel": "Отмена", "change": "Изменить", "changeTheme": "Сменить тему", "comment": "Комментарий", "confirm": "Подтвердить", "currency": "Валюта", "dataFetchingProblem": "Временная проблема с выводом данных", "date": "Дата", "deleted": "Удалено", "didntPlay": "Не играл", "dist_s": "Ди<PERSON>т", "distance": "Дистанция", "done": "Готово", "endDate": "Дата завершения", "enterResults": "Внести", "ev": "Ожидание", "excellent": "Замечательно", "finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finished": "Завер<PERSON>ен", "fishes": "Фиши", "gameStats": "Игровая статистика", "good": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hide": "Скрыть", "k": "к", "ko": "KO", "legend": "Легенда", "load": "Загрузка", "lvl": "Уровень", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Название", "noAccess": "Нет доступа", "noAccessMessage": "У вас нет доступа к данному ресурсу. Пожалуйста обратитесь к администратору", "noProblemsFound": "Проблем не обнаружено", "notAllowed": "Запрещено", "notifications": "Уведомления", "numberOfRooms": "Количество румов", "numberOfTournaments": "Количество турниров", "off": "Выключить", "ok": "Понятно", "on": "Включить", "palette": "Палитра", "password": "Пароль", "period": "Период", "perYear": "за год", "play": "Играть", "played": "Сыграл", "processing": "Обрабатывается", "profile": "Профиль", "progress": "Прогресс", "rank": "<PERSON><PERSON><PERSON><PERSON>", "reentry": "Re-entry", "rejected": "Отклонено", "remove": "Удалить", "request": "Запросить", "requiredField": "Это поле обязательно", "roi": "РОИ", "room": "Рум", "rooms": "Румы", "save": "Сохранить", "scale": "Шкала", "security": "Безопасность", "send": "Отправить", "sessionDuration": "<PERSON><PERSON><PERSON>на сессии", "show": "Показать", "somethingWentWrong": "Что-то пошло не так", "start": "Старт", "startDate": "Дата начала", "status": "Статус", "summary": "Итог", "time": "Время", "tourney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tourneyType": "Тип турнира", "tryLater": "Попробуйте позже", "type": "Тип", "UILanguage": "Язык интерфейса", "unknownStatus": "Неизвестный статус", "updated": "Обновлено", "updateIn": "Обновление через", "winrate": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "written": "За<PERSON><PERSON><PERSON><PERSON>н", "yourStats": "Ваша статистика"}, "guides": {"abi": "ABI", "active-rooms": "Active rooms", "afs": "AFS", "bad-turbo": "Плохие turbo & hyper турниры", "distance": "Дистанция", "distance-select": "Дистанция", "early-early-mid": "Early-Early/Mid", "ev-by-level": "EV по сложности турниров", "ev-by-rooms": "EV по румам", "full-ring": "Фуллринг турбо ПКО", "month-session": "Кол-во твоих сессий за месяц", "mtt-session": "MTT Session", "profit": "Profit", "reentry": "Реентри", "reg-speed": "Регспид ПКО", "roi": "ROI", "select-rating": "Оценка по селекту", "top-3-wins": "Топ-3 заноса", "tournaments": "Памятка"}, "navMenu": {"courses": "FF обучение", "finances": "Финан<PERSON>ы", "gameStats": "Игровая статистика", "main": "Главная", "select": "Селект", "settings": "Настройки", "tournaments": "Турни<PERSON>ы"}, "referal": {"commonStatistic": {"most_friend": "Пригла<PERSON><PERSON><PERSON> самый активный игрок", "people_join": "Вступили в фонд в этом месяце", "title": "Общая статистика"}, "dashboard": {"button": "Пригласить друга", "description": "Получай приятные бонусы за каждого приведенного друга", "title": "Пригла<PERSON><PERSON>й друзей"}, "faq": {"1_answer": "Сумма бонуса зависит от среднего ABI вступившего друга. Сумма варьируется от 100$ до 3500$. Вот точный перечень распределения сумм:\n\nНовички:\nИгрок, заключивший контракт с фондом после прохождения курса FFStart — 100$\n\nГотовые игроки:\nABI 1-5 — 150$\nABI 5-10 — 200$\nABI 10-15 — 300$\nABI 15-20 — 500$\nABI 20-30 — 700$\nABI 30-40 — 1000$\nABI 40-50 — 1500$\nABI 50-60 — 2000$\nABI 60-100 — 2500$\nABI 100+ — 3500$\n\nЧтобы получить выплату, друг должен вступить в фонд по твоей ссылке или сообщить, что он от тебя, устно/письменно, заключить контракт, пройти тестовый период и не быть исключенным по его истечении. Также он не должен воспользоваться правом ухода по истечение тестового периода (он длится также месяц, но возможны нюансы) самостоятельно. После выполнения всех условий мы начислим бонус с 1 по 10 число следующего календарного месяца.", "1_question": "Какой размер выплаты я получу и в каких случаях?", "2_answer": "Приглашай тех, кто интересуется или уже играет в онлайн-покер, заинтересован в карьерном росте. Наверняка у тебя есть знакомые в других фондах, которые подумывают о смене места, или те, кто, как любитель, играет от себя. Мы думаем, ты сможешь рассказать про основные преимущества FF. Если твоих знаний не хватает, то после регистрации наши админы помогут заполнить пробелы в знаниях твоему другу. Главное, чтобы рекомендация была искренней и основанной на личном опыте. Также имеет значение контекст – когда возникает релевантное обсуждение с друзьями в чате или личке, можно удачно поделиться своей ссылкой. \nНе стоит скрывать, что это реферральная программа – многие знают, что это такое, из опыта работы с банками, с приложениями и т.п. Также не стоит скрывать свою заинтересованность, чтобы человек именно пришел в фонд. Так вы сможете быть в одном комьюнити, ну и ты получишь награду, да. \n", "2_question": "Как выбрать друга для рекомендации?", "3_answer": "Расскажи, что ты рекомендуешь крутой покерный фонд, где он получит поддержку, лучшее обучение, бесконечный банкролл и комьюнити жадных до побед единомышленников. \nДобиться результата в команде профессионалов проще, чем в одиночку. Можешь использовать этот тезис в качестве аргумента. \nУ нас чётко выстроен учебный процесс, сильные айти решения. Фонд существует больше 10 лет, и за всё это время не прогорел, а значит, понимает, как сделать так, чтобы игроки выигрывали. \n", "3_question": "Что ему сказать?", "4_answer": "Скопируй свою персональную ссылку и отправь другу. Объясни ему, что важно подать заявку именно по этой ссылке, иначе система не зафиксирует твоего участия в привлечении игрока. ", "4_question": "Как поделиться индивидуальной ссылкой, чтобы бонус зашел мне?", "5_answer": "Друг должен перейти по твоей реферальной ссылке, заключить контракт с фондом, пройти тестовый период, не расторгнуть его в течение тестового периода (обычно он длится месяц, но бывают нюансы), и не быть кикнутым. \n\nПри выполнении этих условий ты получишь выплату с 1 по 10 число следующего календарного месяца. Например, твой друг зарегистрировался 13 августа, подписал контракт 15 августа, и стал играть. Наступило 15 сентября, он не изъявил желания покинуть проект сам и не был кикнут за что-то. Тогда выплата тебе придет с 1 по 10 октября.\nЕсли человек был определён в FF.Start и после его прохождения заключил контракт с фондом, например, 15 августа, то ты получишь 100$ с 1 по 10 октября.\n", "5_question": "Что нужно сделать, чтобы мы начислили бонус?", "title": "Частые вопросы"}, "linkBlock": {"copy": "Ссылка скопирована!", "description": "Делись ссылкой-приглашением с друзьями", "link_title": "Ссылка для приглашения друзей", "share": "Поделиться", "title": "Приглашай друзей, получай бонусы"}, "myStatistic": {"friends_contract": "Друзей заключили контракт", "friends_invites": "Друзей подало заявку", "money_received": "Выплат получено", "title": "Моя статистика"}, "stepsBlock": {"block_title": "Как получить награду?", "step_1_description": "Скопируй персональную ссылку или отправь QR-код другу для вступления в фонд", "step_1_title": "Пригласи друга", "step_2_description": "Друг должен заключить контракт с фондом и не выйти в течение тестового периода. Также его не должны за это время кикнуть", "step_2_title": "Ожидай выполнения условий", "step_3_description": "Бонус по реферальной системе начисляется с 1 по 10 число следующего месяца, после выполнения условий получения бонуса", "step_3_title": "Получи выплату"}}, "select": {"abi": "ABI", "activeRooms": "Кол-во активных румов", "afs": "АФС", "badTurboHyperTourney": "Плохие turbo & hyper турниры", "country": "Страна", "distance": "Дистанция", "earlyEarlyMid": "Ранние вылеты", "fullringRegspeedFreezout": "Fullring Regspeed Freezout", "fullringTurboPko": "Fullring turbo PKO", "mttSession": "Тур<PERSON><PERSON><PERSON><PERSON> за сессию", "ratingSelect": "Оценка селекта", "reentry": "Re-entry", "regspeedPko": "Regspeed PKO", "sessionsCountPerMonth": "Кол-во сессий за месяц", "shorthandedRgspeedFreezout": "Shorthanded Regsp<PERSON> Freezout", "shorthandedTurboHyperPko": "Shorthanded turbo & hyper PKO"}, "settings": {"changeAvatar": "Вы можете изменить фото своего профиля", "changePassword": "Изменение пароля", "changePasswordForSecurity": "Измените ваш текущий пароль для повышения безопасности", "createNewPassword": "Придумайте новый пароль", "disable2fa": "Вы уверены, что хотите отключить двухфакторную аутентификацию?", "enable2faForSecurity": "Включите дополнительный уровень защиты аккаунта", "get2faCode": "Верификация с помощью приложения Google Authenticator. Введите 6-значный код, сгенерированный приложением для аутентификации", "newPassword": "Новый пароль", "notificationsSettings": "Настройка уведомлений", "oldPassword": "Старый пароль", "passwordRequirements": "Пароль должен состоять минимум из 8 символов, содержать цифры и специальные символы (! “ # $ % () *)", "passwordsDoNotMatch": "Пароли не совпадают", "repeatPassword": "Повторите пароль", "scanQRCode": "Отсканируйте QR-код или добавьте код вручную, чтобы добавить приложение в Google Authenticator", "selectNotifications": "Выберите входящие уведомления, которые вы хотели бы получать через Discord", "selectUILanguage": "Выберите язык, который будет использоваться в интерфейсе", "setupNotifications": "Настройте уведомления, которые вы хотите получать"}, "statistics": {"acceptableWR": "С учётом потенциальной дисперсии винрейт должен быть не ниже", "allHands": "Все руки", "atAbi": "На твоем ABI", "average": "Среднее", "avgByLeague": "Среднее по лиге", "avgByRank": "Среднее по рангу", "awaitingInspection": "Ожидают проверки", "awaitingVerification": "Ожидает проверки", "baseEvaluation": "Оценка базы", "baseQuality": "качество базы", "bbWinrate": "бб/100, винрейт", "blindLevel": "Уровень блайндов", "blockWillOpenAfterStudying": "Блок откроется после изучения данной темы", "borderline": "граничные", "borderlineIndicator": "Гра<PERSON><PERSON><PERSON>ный", "boundaryIndicators": "Граничные показатели", "boundaryIndicatorsText": "Требуют повышенного внимания. Держи их на контроле и фокусируйся вокруг необходимых спотов во время каждой сессии.\nДопускается небольшое количество граничных показателей. И абсолютно нормально, что они будут перемещаться из граничных в отличные и обратно. Но не допустимо чтобы граничные показатели перемещались в ужасные.", "buildAnalytics": "Построить аналитику", "buildStatistics": "Построить статистику", "byBlinds": "По уровню блайндов", "byDifficulty": "По сложности турниров", "byHUD": "По наличию HUD", "byNumberOfHands": "По количеству рук", "byPeriod": "По периоду", "byPosition": "По позиции", "byPositions": "По позициям", "byStackSize": "По размеру стека", "byTableSize": "По типу стола", "byTournamentDifficulty": "По сложности турниров", "byTourneyType": "По типу турнира ", "comparisonOfStatistics": "Сравнение статистики", "colorTheStats": "Раскрасить статы", "category": "Категория", "charactersLeftToWrite": "Осталось написать символов", "correctTheIndicator": "Исправить показатель", "currentIndicator": "Текущий показатель", "comment": "Комментарий", "comments": "Комментарии", "correct": "Исправить", "correctionOfTheIndicator": "Исправление показателя", "congratulationsYouCompletedTasks": "Поздравляем, ты выполнил все задания!", "dateOfTheProblem": "Дата проблемы", "describeTheProblem": "Опишите какие действия были предприняты, чтобы исправить проблему. Комментарий минимум 300 символов.", "done": "Выполнено", "duplicateFilters": "Продублировать\nфильтры", "eVByRooms": "EV по румам", "excellent": "отличные", "excellentIndicator": "Отличный", "excellentIndicators": "Отличные показатели", "excellentIndicatorsText": "Старайся чтобы все твои показатели попадали в категорию отличные. Но помни, что каждый стат должен быть наполнен качественно, в соответствии с рекомендациями, которые выдает тренер.\nКатегорически запрещается открывать 72о из UTG для того чтобы накрутить отличный показатель!", "execute": "Выполнить", "gotAcquainted": "Ознакомился", "handCount": "Количество рук", "handsSelection": "рук выборка", "howToWorkWithStatistics": "Как работать со статистикой", "indicator": "Показатель", "indicatorHasBeenMovedToStatus": "Показатель “{{indicator}}” переведен\nв статус “{{status}}”", "instruction": "Инструкция", "instructionsForCorrectingTheIndicator": "Инструкция к исправлению показателя “{{indicator}}”", "inWork": "В работе", "kHandsEach": "По {{count}}к рук", "kUntilNextCheck": "До следующей проверки осталось {{value}}к", "lastK": "Последние {{count}}к", "lastKHands": "Последние {{value}}к рук", "lastKInTheRoom": "Последние {{count}}к в руме", "lastSystemCheck": "Последняя проверка системы", "look": "Смотреть", "markTasksAfterWork": "Отмечай задания только после того, как детально проработаешь рекомендации", "myIndicator": "Мой показатель", "myValue": "Мое значение", "myWinrate": "Мой винрейт", "new": "Новые", "noProblematicIndicators": "Проблемных показателей нет", "notEnoughDataForEvaluation": "Недостаточно данных для оценки", "notEnoughSample": "Недостаточная выборка.\nВыбери больший период", "now": "Сей<PERSON><PERSON>с", "numberOfAssessedIndicators": "Количество оцениваемых\nпоказателей", "previousIndicator": "Прошлый показатель", "problematicIndicator": "Проблемный показатель", "problematicIndicators": "Проблемные показатели", "progressOfImplementation": "Прогресс выполнения", "recommended": "Рекомендуемый", "resetAllFilters": "Сбросить все\nфильтры", "roomsWithHUD": "Румы с HUD", "roomsWithoutHUD": "Румы без HUD", "sample": "Выборка", "sampleOfKHands": "Выборка {{value}}к рук", "smallSample": "Малая выборка", "square": "Ква<PERSON><PERSON><PERSON><PERSON>", "stackSize": "Размер стека", "statusOfTheProblemChanged": "Статус проблемы изменен. После проверки системы,\nесли проблема решена корректно, она удалится из списка", "systemCheck": "Проверка системы", "terrible": "ужасные", "terribleIndicator": "Ужасный", "terribleIndicators": "Ужасные показатели", "terribleIndicatorsText": "Не допускай чтобы твои показатели попали в категорию ужасных.\nЕсли такой показатель появился - мы выдаем задание на исправление.\nПриложи максимум усилий чтобы исправить показатель.", "theProblemHasBeenWorkedOut": "Проблема проработана", "total": "Итог", "totalIndicatorsProcessed": "Всего проработанных показателей", "toTheRecommendations": "К рекомендациям", "unrated": "Неоцениваемый", "was": "Было", "watchedRecommendedVideos": "Посмотрел рекомендованные видео", "wellDeveloped": "Проработанные", "winrateInSections": "Винрейт в разрезах", "worked": "Проработал", "workingThroughTheProblem": "Проработка проблемы", "workIsRequired": "Требуется проработка"}, "time": {"runTime": "Идет", "startIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "days_one": "{{count}} день", "days_few": "{{count}} дня", "days_many": "{{count}} д<PERSON><PERSON><PERSON>", "hours_one": "{{count}} час", "hours_few": "{{count}} часа", "hours_many": "{{count}} ч<PERSON><PERSON><PERSON>", "minutes_one": "{{count}} минуту", "minutes_few": "{{count}} минуты", "minutes_many": "{{count}} минут"}, "tournaments": {"addTemplate": "Добавить шаблон", "alwaysInPackage": "Постоянно в пакет", "editTemplate": "Редактировать шаблон", "enterResults": "Внести результаты турнира от себя", "enterTemplateName": "Введите название шаблона", "foundTourneys": "Найденные турниры", "freezeOut": "Фризаут", "freezeOutRebuy": "Фризаут-Ребайник", "fromSelf": "От себя", "hidden": "Скрытые", "intoThePackage": "В пакет", "invalidTimeFormat": "Неверный формат времени", "koRebuy": "KO-Ребайник", "limitsForRooms": "Лимиты для румов, которых нет в основной таблице", "manualInputTourney": "Указать турнир в ручную", "minBuyin": "<PERSON><PERSON><PERSON>-ин", "partOfThePackage": "50% от себя", "past": "Прошедшие", "rebuyDefinition": "Ребайник - это турнир, в котором сразу же после начала можно докупить дополнительные фишки. Плюс есть аддон – возможность купить фишки в перерыве по улучшенному курсу. Другие турниры, например, где можно вылететь и сделать реентри - это турниры с реентри и к ребайникам отношения не имеют", "regspeed6max": "Регспид 6-макс", "regspeed6maxDefinition": "6 или меньше людей за столом и уровни по 7 минут или больше", "regspeedFullring": "Регспид фуллринг", "regspeedFullringDefinition": "7 или больше людей за столом и уровни по 7 минут или больше", "requestAdmission": "Запросить разрешение", "series": "Регулярки/Серии", "setupLegend": "Настроить легенду", "setValuesForTemplate": "Укажите значения, которые будут применяться при выборе шаблона", "showTournamentsList": "Показать список турниров", "templateName": "Имя шаблона", "to": "по", "tournamentAdmissions": "Разрешения на турниры", "tourneyName": "Название турнира", "transactionWillBeRecorded": "В разделе \"Финансы\" транзакция запишется автоматически", "turbo6max": "Турбо 6-макс", "turbo6maxDefinition": "6 или меньше людей за столом и уровни по 4-6 минут", "turboFullring": "Турбо фуллринг", "turboFullringDefinition": "7 или больше людей за столом и уровни по 4-6 минут", "willNotPlay": "Не буду играть", "willPlay": "Буду играть"}, "all": "Все", "fetchingError": "Произошла ошибка при загрузке данных. Попробуйте обновить страницу или попробуйте позже.", "$/tour": "$/тур", "3 months": "3 месяца", "6 months": "6 месяцев", "About tourney": "О турнире", "All": "ALL", "All filters": "Все фильтры", "Avg at abi": "Среднее на твоём аби", "Buy-in": "Бай-ин", "Cancel": "Отменить", "Create template": "Добавить шаблон", "Current month": "Текущий месяц", "Current year": "Текущий год", "Daily": "Загрузка по дням", "Delete": "Удалить", "Don't played": "Не играл", "Edit template": "Редактировать шаблон", "Finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Finished": "Завер<PERSON>ен", "Fishes": "Фиши", "Hidden": "Скрытые", "Hide": "Скрыть", "In": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Load by rooms": "Загрузка по румам", "Login": "Войти", "Min Buy-in": "<PERSON><PERSON><PERSON>-ин", "Name": "Название", "Past": "Прошедшие", "Played": "Сыграл", "Postflop": "Постфлоп", "Preflop": "Префлоп", "Prev month": "Прошлый месяц", "Prev year": "Прошлый год", "Privacy policy": "Политика конфиденциальности", "Profit": "Profit", "Rank": "<PERSON><PERSON><PERSON><PERSON>", "Recommended": "Рекомендация", "Regular": "Регулярки", "Regular/Series": "Регулярки/Серии", "Room": "Рум", "Rooms": "Румы", "rooms played": "румов сыграно", "Running": "Идет", "Save template": "Сохранить шаблон", "Series": "Серии", "Setup columns": "Настроить колонки", "Show": "Показать", "Start": "Старт", "Status": "Статус", "Summary": "Итог", "Time": "Время", "time spent": "затрачено времени", "Tourney type": "Тип турнира", "Type": "Тип", "Will play": "Буду играть", "Winrate": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "day_one": "день", "day_few": "дня", "day_many": "<PERSON><PERSON><PERSON><PERSON>", "day_short": "д", "hand_one": "рука", "hand_few": "рук", "hand_many": "рук", "hand_short": "р", "hour_other": "<PERSON><PERSON><PERSON><PERSON>", "hour_one": "час", "hour_few": "часа", "hour_many": "<PERSON><PERSON><PERSON><PERSON>", "hour_short": "ч", "last_one": "последний", "last_few": "последних", "last_many": "последних", "last_short": "посл", "min_other": "минут", "min_one": "минуту", "min_few": "минуты", "min_many": "минут", "min_short": "мин", "month_one": "мес<PERSON><PERSON>", "month_few": "месяца", "month_many": "меся<PERSON>ев", "month_short": "мес", "selected_one": "выбран", "selected_few": "выбрано", "selected_many": "выбрано", "year_one": "год", "year_few": "года", "year_many": "лет", "year_short": "г"}