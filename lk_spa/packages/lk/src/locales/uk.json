{"auth": {"authorization": "Авторизація", "emailOrNickname": "Адреса ел. пошти / нікнейм", "password": "Пароль"}, "dashboard": {"12monthsResults": "Результат за останні 12 місяців", "abiForTheLastMonth": "ABI за останній місяць", "abiTop": "Ви у ТОП {{value}}% по ABI", "achievements": "Досягнення", "all": "Усі", "BERTooltip": "Показує, наскільки ефективно ви використовуєте виділені кошти. Обчислюється як відношення завантаження за день до максимального залишку коштів проекту. Чим вищий КЕБ, тим краще.", "continueLearning": "Продовжити навчання", "currentSection": "Поточний розділ", "editAvatar": "Редагувати аватар", "finance": "Фінанси", "hoursOfEducation": "Годинник навчання", "last100k": "За останні 100к рук", "lastMonth": "За останній місяць", "league": "Ліга", "leagueTop": "Ви у ТОП {{value}}% по лігі", "needToWorkOn": "Потрібно проробити", "overallProgress": "Загальний прогрес", "playersPath": "<PERSON>л<PERSON><PERSON> гравця", "selectRating": "Оцінка селекту", "sendOverage": "Потрібно відправити", "studiedForHours": "Навчався годинника", "tasksCompleted": "Завдань виконано", "timeInTheProject": "Час у проекті", "toEducation": "До навчання", "top3": "Top 3 Cashes", "indicator_one": "{{count}} показник", "indicator_few": "{{count}} показника", "indicator_many": "{{count}} показників"}, "education": {"allTasks": "Усі завдання", "baseCheckNotEnoughData": "Оцінка з'явиться після того, як ви зagraли {{reqTourneysCount}} турнірів. На даний момент даних для аналізу не достатньо", "baseCheckPrevLevel": "Проміжна оцінка на основі результатів {{prevLevelTourneysCount}} турнірів з попереднього рівня. Як тільки на поточному рівні буде зagraно {{reqTourneysCount}} турнірів, ми оновимо оцінку. Продовжуйте грати та виконувати завдання", "baseCheckPrevLevelNotEnoughHands": "Проміжна оцінка на основі результатів {{prevLevelTourneysCount}} турнірів з попереднього рівня. Ви виконали всі завдання та зagraли дистанцію на поточному рівні, але для оцінки бази потрібно мінімум {{minReqHands}} рук (зараз {{currentHandsCount}}). Як тільки це умова буде виконана, з'явиться оцінка на поточному рівні замість попередньої", "baseCheckCurrentLevelIncomplete": "Проміжна оцінка на основі результатів {{tourneysCount}} турнірів на рівні. Остаточна оцінка буде доступна після зagraву {{reqTourneysCount}} турнірів та виконання 100% завдань на рівні. Продовжуйте грати та виконувати завдання", "baseCheckCurrentLevelTourneysIncomplete": "Проміжна оцінка на основі результатів {{tourneysCount}} турнірів на рівні. Остаточна оцінка буде доступна після зagraву {{reqTourneysCount}} турнірів на рівні. Продовжуйте грати та виконувати завдання", "baseCheckCurrentLevelTasksIncomplete": "Проміжна оцінка на основі результатів {{tourneysCount}} турнірів на рівні. Остаточна оцінка буде доступна після виконання 100% завдань на рівні. Продовжуйте грати та виконувати завдання", "baseCheckLevelUp": "Оцінка на основі результатів обробки {{tourneysCount}} турнірів. Поріг 50% досягнуто - ви готові до наступного рівня", "baseCheckLevelUpNotEnough": "Оцінка на основі результатів обробки {{tourneysCount}} турнірів. Для переходу на наступний рівень оцінка бази повинна бути 50% або вище. Продовжуйте грати, виправляти помилки та покращувати свою гру", "baseCheckLevel": "Перевірка бази, %", "baseCheckUnavailable": "Перевірка бази буде доступна після завершення попередніх прогрес-барів", "check": "Перевірити", "clickToCopyYourNickname": "Клацніть, щоб скопіювати свій нік", "completeChapter": "Завершити розділ", "completedTasks": "Виконано завдань", "completeLesson": "Завершити урок", "continue": "Продовжити", "continueTasks": "Продовжити завдання", "distanceLevel": "Дистанція, МТТ", "downloadPdf": "Завантажити pdf", "education": "Навчання", "from": "з", "goThroughTheChapter": "Пройдіть розділ", "goToTask": "Перейти до завдання", "hoursOfTraining": "Годин навчання", "levelUpMessage": "Грайте у турніри та проходьте уроки, щоб підвищити свій рівень", "minutesOfTraining": "<PERSON><PERSON>и<PERSON>ин навчання", "next": "<PERSON><PERSON><PERSON><PERSON>", "nextLesson": "Наступний урок", "noCoursesAvailable": "Немає доступних курсів", "passed": "Пройдено", "prev": "Назад", "prevLesson": "Попередній урок", "rateTaskDifficulty": "Оцініть складність завдання", "rateTaskInterestingness": "Оцініть цікавість завдання", "repeat": "Повторити", "requestVerification": "Запитати перевірку", "seeMore": "Подивитися ще", "startLearning": "Почати навчання", "step": "Крок", "stepsTaken": "Кроків пройдено", "successfulTask": "успішне завдання", "taskCompleted": "Завдання виконане!", "taskProgress": "Прогрес за завданням", "tasksCompleted": "Завдань пройдено", "tasksCompletionInProfile": "Виконання завдань буде вказано у твоєму профілі", "tasksLevel": "Завдання, %", "hours_one": "година", "hours_few": "години", "hours_many": "годин", "minutes_one": "хвилина", "minutes_few": "хвилини", "minutes_many": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tasks_one": "завдання", "tasks_few": "завдання", "tasks_many": "завдань"}, "finances": {"balanceHistory": "Історія заповнення балансів", "packageClosed": "Пакет закрито", "packageOnCheckout": "Пакет на розрахунку", "packageOpened": "Пакет відкрито", "packageProfit": "Профіт пакета"}, "global": {"$/tour": "$/тур", "2fa": "Двухфакт<PERSON><PERSON>на аутентифікація", "abi": "АБІ", "aboutTourney": "Про турнір", "add": "Додати", "afs": "АФС", "all": "Усі", "allFilters": "Всі фільтри", "allTourneys": "Усі турніри", "answer": "Відповідь", "avatar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bad": "Погано", "BER": "КЭБ", "BER_full": "Коефіцієнт ефективності балансів", "buyin": "Бай-ін", "cancel": "Скасування", "change": "Змінити", "changeTheme": "Змінити тему", "comment": "Коментар", "confirm": "Підтвердити", "currency": "Валюта", "dataFetchingProblem": "Тимчасова проблема з виведенням даних", "date": "Дата", "deleted": "Видалено", "didntPlay": "Не грається", "dist_s": "Ди<PERSON>т", "distance": "Дистанція", "done": "Готово", "endDate": "Дата завершення", "enterResults": "Ввести", "ev": "Очікування", "excellent": "Чудово", "finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finished": "Завершено", "fishes": "Ф<PERSON>ш<PERSON>", "gameStats": "Ігрова статистика", "good": "Добре", "hide": "Приховати", "k": "к", "ko": "KO", "legend": "Легенда", "load": "Завантаження", "lvl": "Рівень", "month": "Місяць", "name": "Назва", "noAccess": "Немає доступу", "noAccessMessage": "У вас немає доступу до цього ресурсу. Будь ласка, зверніться до адміністратора", "noProblemsFound": "Проблем не знайдено", "notAllowed": "Не дозволено", "notifications": "Повідомлення", "numberOfRooms": "Кількість румів", "numberOfTournaments": "Кількість турнірів", "off": "Вимкнути", "ok": "Зрозуміло", "on": "Увімкнути", "palette": "Палітра", "password": "Пароль", "period": "Період", "perYear": "за рік", "play": "<PERSON>ра<PERSON>и", "played": "Грається", "processing": "Обробляється", "profile": "Профіль", "progress": "Прогрес", "rank": "<PERSON><PERSON><PERSON><PERSON>", "reentry": "Re-entry", "rejected": "Від<PERSON>илено", "remove": "Видалити", "request": "Запросити", "requiredField": "Це поле обов'язкове", "roi": "РОІ", "room": "Рум", "rooms": "Руми", "save": "Зберегти", "scale": "Масш<PERSON><PERSON><PERSON>", "security": "Безпека", "send": "Надіслати", "sessionDuration": "<PERSON>ов<PERSON><PERSON>на сесії", "show": "Показати", "somethingWentWrong": "Щось пішло не так", "start": "Старт", "startDate": "Дата початку", "status": "Статус", "summary": "Ітог", "time": "<PERSON><PERSON><PERSON>", "tourney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tourneyType": "Тип турніру", "tryLater": "Спробуйте пізніше", "type": "Тип", "UILanguage": "Мова інтерфейсу", "unknownStatus": "Невідомий статус", "updated": "Оновлено", "updateIn": "Оновлення через", "winrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "written": "Записано", "yourStats": "Ваша статистика"}, "guides": {"abi": "АБІ", "active-rooms": "Active rooms", "afs": "AFS", "bad-turbo": "Погані turbo & hyper турніри", "distance": "Дистанція", "distance-select": "Дистанція", "early-early-mid": "Early-Early/Mid", "ev-by-level": "EV по рівню складності турнірів", "ev-by-rooms": "EV по румам", "full-ring": "Фуллрінг турбо ПКО", "month-session": "Кількість твоїх сесій за місяць", "mtt-session": "MTT Session", "profit": "Прибуток", "reentry": "Реєнтрі", "reg-speed": "Регспід ПКО", "roi": "ROI", "select-rating": "Оцінка по селекту", "top-3-wins": "Топ-3 вигра<PERSON>і", "tournaments": "Пам'ятка"}, "navMenu": {"courses": "FF навчання", "finances": "Фінанси", "gameStats": "Ігрова статистика", "main": "Головна", "select": "Селект", "settings": "Налаштування", "tournaments": "Турні<PERSON>и"}, "referal": {"commonStatistic": {"most_friend": "Запросив найактивніший гравець", "people_join": "Вступили до фонду цього місяця", "title": "Загальна статистика"}, "dashboard": {"button": "Запросити друга", "description": "Отримуй приємні бонуси за кожного наведеного друга", "title": "Запро<PERSON>уй друзів"}, "faq": {"1_answer": "Сума бонусу залежить від середнього ABI вступника. Сума варіюється від 100 $ до 3500 $. Ось точний перелік розподілу сум:\n\nНовачки:\nГрав<PERSON><PERSON><PERSON>, який уклав контракт із фондом після проходження курсу FFStart — 100$\n\nГотові гравці:\nABI 1-5 — 150$\nABI 5-10 — 200$\nABI 10-15 — 300$\nABI 15-20 — 500$\nABI 20-30 — 700$\nABI 30-40 — 1000$\nABI 40-50 — 1500$\nABI 50-60 — 2000$\nABI 60-100 — 2500$\nABI 100+ — 3500$\n\nЩоб отримати виплату, друг повинен вступити до фонду за твоєю посиланням або повідомити, що він від тебе, усно/письменно, укласти контракт, пройти тестовий період і не бути виключеним після його закінчення. Також він не повинен скористатися правом догляду після закінчення тестового періоду (він триває також місяць, але можливі нюанси) самостійно. Після виконання всіх умов ми нарахуємо бонус з 1 до 10 числа наступного календарного місяця.", "1_question": "Який розмір виплати я отримаю і у яких випадках?", "2_answer": "Запрошуй тих, хто цікавиться або вже грає в онлайн покер, зацікавлений у кар'єрному зростанні. Напевно, у тебе є знайомі в інших фондах, які подумують про зміну місця, або ті, хто, як любитель, грає від себе. Ми думаємо, що ти зможеш розповісти про основні переваги FF. Якщо твоїх знань не вистачає, то після реєстрації наші адміни допоможуть заповнити прогалини у знаннях твого друга. Головне, щоб рекомендація була щирою та заснованою на особистому досвіді. Також має значення контекст – коли виникає релевантне обговорення з друзями у чаті чи личку, можна вдало поділитись своїм посиланням. \nНе варто приховувати, що це реферальна програма – багато хто знає, що це таке, з досвіду роботи з банками, з додатками тощо. Також не варто приховувати свою зацікавленість, щоб людина саме прийшла до фонду. Так ви зможете бути в одному ком'юніті, та й отримаєш нагороду, так. \n", "2_question": "Як вибрати друга для поради?", "3_answer": "Розкажи, що ти рекомендуєш крутий фонд покеру, де він отримає підтримку, найкраще навчання, нескінченний банкрол і ком'юніті жадібних до перемог однодумців. \nДомогтися результату в команді професіоналів простіше, ніж самотужки. Можеш використовувати цю тезу як аргумент. \nУ нас чітко побудований навчальний процес, сильні рішення. Фонд існує більше 10 років, і за цей час не прогорів, а значить, розуміє, як зробити так, щоб гравці вигравали. \n", "3_question": "Що йому сказати?", "4_answer": "Скопіюй своє персональне посилання та відправь другу. Поясні йому, що важливо подати заявку саме за цим посиланням, інакше система не зафіксує твоєї участі у залученні гравця.", "4_question": "Як поділитись індивідуальним посиланням, щоб бонус зайшов мені?", "5_answer": "Друг повинен перейти за твоєю реферальною посиланням, укласти контракт із фондом, пройти тестовий період, не розірвати його протягом тестового періоду (зазвичай він триває місяць, але бувають нюанси), і не бути кикнутим. \n\nЗа виконання цих умов ти отримаєш виплату з 1 по 10 число наступного календарного місяця. Наприклад, твій друг зареєструвався 13 серпня, підписав контракт 15 серпня і почав грати. Настало 15 вересня, він не виявив бажання покинути проект сам і не був кікнутий за щось. Тоді виплата тобі прийде з 1 до 10 жовтня.\nЯкщо людина була визначена у FF.Start і після її проходження уклала контракт із фондом, наприклад, 15 серпня, то ти отримаєш 100$ з 1 по 10 жовтня.\n", "5_question": "Що потрібно зробити, щоб ми нарахували бонус?", "title": "Часті запитання"}, "linkBlock": {"copy": "Посилання скопійоване!", "description": "Поділися посиланням-запрошенням із друзями", "link_title": "Посилання для запрошення друзів", "share": "Поділити<PERSON>я", "title": "Запрошуй друзів, отримуй бонуси"}, "myStatistic": {"friends_contract": "Друзів уклали контракт", "friends_invites": "Друзі подало заявку", "money_received": "Виплат отримано", "title": "Моя статистика"}, "stepsBlock": {"block_title": "Як отримати нагороду?", "step_1_description": "Скопіюй персональне посилання або надішліть QR-код другу для вступу до фонду", "step_1_title": "Запроси друга", "step_2_description": "Друг повинен укласти контракт із фондом і не вийти протягом тестового періоду. Також його не повинні за цей час кикнути", "step_2_title": "Чекай виконання умов", "step_3_description": "Бонус за реферальною системою нараховується з 1 по 10 число наступного місяця після виконання умов отримання бонусу", "step_3_title": "Отримай виплату"}}, "select": {"abi": "ABI", "activeRooms": "Кількість активних румів", "afs": "АФС", "badTurboHyperTourney": "Погані turbo & hyper турніри", "country": "Країна", "distance": "Дистанція", "earlyEarlyMid": "Ранні вильоти", "fullringRegspeedFreezout": "Fullring Regspeed Freezout", "fullringTurboPko": "Fullring turbo PKO", "mttSession": "Тур<PERSON><PERSON><PERSON><PERSON>в за сесію", "ratingSelect": "Оцінка селекта", "reentry": "Re-entry", "regspeedPko": "Regspeed PKO", "sessionsCountPerMonth": "Кількість сесій за місяць", "shorthandedRgspeedFreezout": "Shorthanded Regsp<PERSON> Freezout", "shorthandedTurboHyperPko": "Shorthanded turbo & hyper PKO"}, "settings": {"changeAvatar": "Ви можете змінити фото свого профілю", "changePassword": "<PERSON><PERSON><PERSON>на пароля", "changePasswordForSecurity": "Змініть свій поточний пароль для підвищення безпеки", "createNewPassword": "Створіть новий пароль", "disable2fa": "Ви впевнені, що хочете вимкнути двофакторну аутентифікацію?", "enable2faForSecurity": "Увімкніть додатковий рівень безпеки для вашого облікового запису", "get2faCode": "Верифікація за допомогою додатку Google Authenticator. Введіть 6-значний код, згенерований додатком для аутентифікації", "newPassword": "Новий пароль", "notificationsSettings": "Налаштування сповіщень", "oldPassword": "Старий пароль", "passwordRequirements": "Пароль повинен містити принаймні 8 символів, числа та спеціальні символи (! “ # $ % () *)", "passwordsDoNotMatch": "Паролі не збігаються", "repeatPassword": "Повторіть пароль", "scanQRCode": "Скануйте QR-код або додайте код вручну, щоб додати додаток до Google Authenticator", "selectNotifications": "Виберіть вхідні повідомлення, які ви бажаєте отримати через Discord", "selectUILanguage": "Виберіть мову, яку ви б хотіли використовувати в інтерфейсі", "setupNotifications": "Налаштуйте сповіщення, які ви хочете отримувати"}, "statistics": {"acceptableWR": "З потенційною дисперсією вінрейт повинен бути не нижчим за", "allHands": "Усі руки", "atAbi": "На твоєму АБІ", "average": "Середнє", "avgByLeague": "Середнє по лізі", "avgByRank": "Середнє по рангу", "awaitingInspection": "Очікують перевірки", "awaitingVerification": "Очікує перевірки", "baseEvaluation": "Оцінка бази", "bbWinrate": "бб/100, вінрейт", "baseQuality": "якість бази", "blindLevel": "Рівень блайндів", "blockWillOpenAfterStudying": "Блок відкриється після вивчення цієї теми", "borderline": "гран<PERSON><PERSON>ні", "borderlineIndicator": "Г<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boundaryIndicators": "Граничні показники", "boundaryIndicatorsText": "Вимагають підвищеної уваги. Тримай їх на контролі та фокусуйся навколо необхідних спотів під час кожної сесії.\nДопускається невелика кількість граничних показників. І абсолютно нормально, що вони переміщатимуться з граничних у відмінні та назад. Але не припустимо, щоб граничні показники переміщалися в жахливі.", "buildAnalytics": "Побудувати аналітику", "buildStatistics": "Збудувати статистику", "byBlinds": "По рівню блайндів", "byDifficulty": "По складності турнірів", "byHUD": "По наявності HUD", "byNumberOfHands": "За кількістю рук", "byPeriod": "За періодом", "byPosition": "По позиції", "byPositions": "По позиціям", "byStackSize": "По розміру стеку", "byTableSize": "По типу столу", "byTournamentDifficulty": "За складністю турнірів", "byTourneyType": "По типу турніру", "comparisonOfStatistics": "Порівняння статистики", "colorTheStats": "Розфарбувати статті", "category": "Категория", "charactersLeftToWrite": "Залишилось написати символів", "correctTheIndicator": "Виправити показник", "currentIndicator": "Поточний показник", "comment": "Коментар", "comments": "Коментарі", "correct": "Виправити", "correctionOfTheIndicator": "Виправлення показника", "congratulationsYouCompletedTasks": "Вітаємо, ти виконав усі завдання!", "dateOfTheProblem": "Дата проблеми", "describeTheProblem": "Опи<PERSON><PERSON>ть, які дії були вжиті, щоб виправити проблему. Коментар щонайменше 300 символів.", "done": "Виконано", "duplicateFilters": "Продублювати\nфільтри", "eVByRooms": "EV за румами", "excellent": "чудові", "excellentIndicator": "Від<PERSON>інний", "excellentIndicators": "Отличные показатели", "excellentIndicatorsText": "Намагайся, щоб усі твої показники потрапляли в категорію відмінні. Але пам'ятай, що кожен стат має бути наповнений якісно, відповідно до рекомендацій, які видає тренер.\nКатегорично забороняється відкривати 72о з UTG для того, щоб накрутити відмінний показник!", "execute": "Виконати", "gotAcquainted": "Ознайомився", "handCount": "Кількість рук", "handsSelection": "рук вибірка", "howToWorkWithStatistics": "Як працювати зі статистикою", "indicator": "Показатель", "indicatorHasBeenMovedToStatus": "Показник “{{indicator}}” перекладено\nу статус \"{{status}}\"", "instruction": "Інструкція", "instructionsForCorrectingTheIndicator": "Інструкція виправлення показника “{{indicator}}”", "inWork": "В роботі", "kHandsEach": "По {{count}}к рук", "kUntilNextCheck": "До наступної перевірки залишилося {{value}}к", "lastK": "Останн<PERSON> {{count}}к", "lastKHands": "Останн<PERSON> {{value}}к рук", "lastKInTheRoom": "Останн<PERSON> {{count}}к у румі", "lastSystemCheck": "Остання перевірка системи", "look": "<PERSON>ив<PERSON><PERSON><PERSON><PERSON><PERSON>", "markTasksAfterWork": "Відзначай завдання лише після того, як детально опрацюєш рекомендації", "myIndicator": "Мій показник", "myValue": "Моє значення", "myWinrate": "<PERSON><PERSON><PERSON> винрейт", "new": "Нові", "noProblematicIndicators": "Проблемних показників немає", "notEnoughDataForEvaluation": "Недостатньо даних для оцінки", "notEnoughSample": "Недостатня вибірка.\nВибери більший період", "now": "За<PERSON><PERSON><PERSON>", "numberOfAssessedIndicators": "Кількість оцінюваних\nпоказників", "previousIndicator": "Попередній показник", "problematicIndicator": "Проблемний показник", "problematicIndicators": "Проблемні показники", "progressOfImplementation": "Прогрес виконання", "recommended": "Рекомендований", "resetAllFilters": "Скинути всі\nфільтри", "roomsWithHUD": "Руми з HUD", "roomsWithoutHUD": "Руми без HUD", "sample": "Вибірка", "sampleOfKHands": "Вибірка {{value}}к рук", "smallSample": "Мала вибірка", "square": "Ква<PERSON><PERSON><PERSON><PERSON>", "stackSize": "Розмір стеку", "statusOfTheProblemChanged": "Статус проблеми змінено. Після перевірки системи,\nякщо проблему вирішено коректно, вона видаляється зі списку", "systemCheck": "Перевірка системи", "terrible": "жахливі", "terribleIndicator": "Жахливий", "terribleIndicators": "Ужасные показатели", "terribleIndicatorsText": "Не допускай, щоб твої показники потрапили до категорії жахливих.\nЯкщо такий показник з'явився – ми видаємо завдання на виправлення.\nПриклади максимум зусиль, щоб виправити показник.", "theProblemHasBeenWorkedOut": "Проблема опрацьована", "total": "Підсумок", "totalIndicatorsProcessed": "Усього опрацьованих показників", "toTheRecommendations": "До рекомендацій", "unrated": "Неоцінюваний", "was": "<PERSON>уло", "watchedRecommendedVideos": "Подивився рекомендовані відео", "wellDeveloped": "Опрацьовані", "winrateInSections": "Вінрейт у розрізах", "worked": "Пропра<PERSON>ю<PERSON>ав", "workingThroughTheProblem": "Опрацювання проблеми", "workIsRequired": "Потрібне опрацювання"}, "time": {"runTime": "Іде", "startIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hours_one": "{{count}} година", "hours_few": "{{count}} години", "hours_many": "{{count}} годин", "minutes_one": "{{count}} хвилину", "minutes_few": "{{count}} хвилини", "minutes_many": "{{count}} хвилин"}, "tournaments": {"addTemplate": "Додати шаблон", "alwaysInPackage": "Завжди в пакет", "editTemplate": "Редагувати шаблон", "enterResults": "Ввести результати турніру від себе", "enterTemplateName": "Введіть назву шаблону", "foundTourneys": "Знайдені турніри", "freezeOut": "Фризаут", "freezeOutRebuy": "Фризаут-Ребайник", "fromSelf": "Від себе", "hidden": "Приховані", "intoThePackage": "В пакет", "invalidTimeFormat": "Невірний формат часу", "koRebuy": "KO-Ребайник", "limitsForRooms": "Ліміти для румів, яких немає в основній таблиці", "manualInputTourney": "Ввести турнір вручну", "minBuyin": "<PERSON><PERSON><PERSON>а<PERSON>-ін", "partOfThePackage": "Частково в пакет", "past": "Прошлі", "rebuyDefinition": "Ребайник - це турнір, в якому відразу після початку можна докупити додаткові фішки. Плюс є аддон - можливість купити фішки в перериві за покращену ставку. Інші турніри, наприклад, де можна вийти і зробити реентри - це турніри з реентрами і до ребайників не мають відношення", "regspeed6max": "<PERSON>ег<PERSON><PERSON>ід 6-макс", "regspeed6maxDefinition": "6 або менше людей за столом і рівні по 7 хвилин або більше", "regspeedFullring": "Регспід фуллринг", "regspeedFullringDefinition": "7 або більше людей за столом і рівні по 7 хвилин або більше", "requestAdmission": "Запросити дозвіл", "series": "Регулярки/Серії", "setupLegend": "Налаштувати легенду", "setValuesForTemplate": "Вкажіть значення, які будуть застосовуватися під час вибору шаблону", "showTournamentsList": "Показати список турнірів", "templateName": "Назва шаблону", "to": "по", "tournamentAdmissions": "Дозволи на турніри", "tourneyName": "Название турнира", "transactionWillBeRecorded": "Транзакція буде записана автоматично в розділі \"Фінанси\"", "turbo6max": "Турбо 6-макс", "turbo6maxDefinition": "6 або менше людей за столом і рівні по 4-6 хвилин", "turboFullring": "Турбо фуллринг", "turboFullringDefinition": "7 або більше людей за столом і рівні по 4-6 хвилин", "willNotPlay": "Не буду грати", "willPlay": "Буду грати"}, "all": "Усі", "fetchingError": "Виникла помилка під час завантаження даних. Спробуйте оновити сторінку або спробуйте пізніше.", "All": "Все", "Current month": "Поточний місяць", "Login": "<PERSON><PERSON><PERSON><PERSON>", "Postflop": "Постфлоп", "Preflop": "Префлоп", "Prev month": "мин<PERSON><PERSON>ий місяця", "Privacy policy": "Політика конфіденційності", "Profit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recommended": "Рекомендації", "day_one": "день", "day_few": "<PERSON><PERSON><PERSON>", "day_many": "<PERSON><PERSON><PERSON><PERSON>", "day_short": "д", "hand_one": "рука", "hand_few": "рук", "hand_many": "рук", "hand_short": "р", "min_other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "min_one": "<PERSON>вил<PERSON><PERSON><PERSON>", "min_few": "хвилини", "min_many": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "min_short": "хв", "month_one": "місяць", "month_few": "мі<PERSON><PERSON><PERSON>і", "month_many": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "month_short": "міс", "selected_one": "обраний", "selected_few": "обрано", "selected_many": "обрано", "year_one": "рік", "year_few": "роки", "year_many": "років", "year_short": "р"}