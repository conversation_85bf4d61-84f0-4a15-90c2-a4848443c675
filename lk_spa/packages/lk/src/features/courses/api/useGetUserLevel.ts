import { ApiRequest } from '@/shared/api';
import { useQuery } from '@tanstack/react-query';

export enum LevelProgressMetric {
  Distance = 'distance',
  Course = 'course',
  BaseCheck = 'base_check',
}

export type MetricType = 'integer' | 'percent enum';

export enum BaseCheckStatus {
  NoData = 'no_data',
  OldDataOtherIncomplete = 'old_data_other_incomplete',
  OldDataOtherComplete = 'old_data_other_complete',
  NewDataOtherIncomplete = 'new_data_other_incomplete',
  NewDataOtherComplete = 'new_data_other_complete',
}

export type DistanceLevelProgress = {
  metric_name: LevelProgressMetric.Distance;
  metric_target: number;
  metric_type: MetricType;
  metric_value: number;
  is_activated: boolean;
  is_actual: boolean;
};

export type CourseLevelProgress = {
  metric_name: LevelProgressMetric.Course;
  metric_target: number;
  metric_type: MetricType;
  metric_value: number;
  is_activated: boolean;
  is_actual: boolean;
};

export type BaseCheckLevelProgress = {
  metric_name: LevelProgressMetric.BaseCheck;
  metric_target: number;
  metric_type: MetricType;
  metric_value: number;
  is_activated: boolean;
  is_actual: boolean;
  next_update_date: string;
  minimum_hand_count: number;
  current_level_hand_count: number;
  state: BaseCheckStatus;
};

export const useGetUserLevel = () =>
  useQuery({
    queryKey: ['user-level'],
    queryFn: () =>
      new ApiRequest<{
        current_course_step: {
          course_id: string;
          block_id: string;
          module_id: string;
          step_id: string;
          module_title: string;
        } | null;
        level: number | null;
        progress: {
          [LevelProgressMetric.Distance]: DistanceLevelProgress;
          [LevelProgressMetric.Course]: CourseLevelProgress;
          [LevelProgressMetric.BaseCheck]: BaseCheckLevelProgress;
        };
        distance_remaining: number | null;
        tasks_remaining: number | null;
        is_level_completed: boolean;
      }>({
        method: 'GET',
        url: '/api/gamification/way/user-way-level-info',
      }),

    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchInterval: (1000 * 60 * 1) / 2, // 30sec minutes
  });
