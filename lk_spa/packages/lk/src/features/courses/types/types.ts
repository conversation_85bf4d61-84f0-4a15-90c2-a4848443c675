export enum CourseGroup {
  Main = 'main',
  Mental = 'mental',
  Newbie = 'newbie',
  Trainer = 'trainer',
  Trigger = 'trigger',
}

export type CourseItemBase = {
  id: string;
  deleted_at: string | null;
  updated_at: string;
  created_at: string;
  description: string;
  title: string;
  passed_length_minutes: number;
  total_length_minutes: number;
  image_url: string;
  is_active: boolean;
  is_available: boolean;
  is_done: boolean;
  is_started: boolean;
  tasks_done: number;
  tasks_locked: number;
  tasks_total: number;
  number: number;
};

export type CourseItem = CourseItemBase & {
  blocks: BlockItem[];
  language: string;
  type: CourseGroup;
  modules_passed: number;
  total_modules_count: number;
  next_step: {
    block_id: string;
    course_id: string;
    module_id: string;
    step_id: string;
  };
};

export type BlockItem = CourseItemBase & {
  course_id: string;
  course_title: string;
  modules: ModuleItem[];
};

export type ModuleItem = CourseItemBase & {
  block_id: string;
  block_title: string;
  course_id: string;
  course_title: string;
  steps: EducationStep[];
};

export type EducationModuleWithSteps = {
  id: string;
  description: string;
  number: number;
  title: string;
  image_url: string;
  total_length_minutes: number;
  steps: EducationStep[];
  block_title: string;
  course_title: string;
  tasks_done: number;
  tasks_total: number;
};

export type ModulePageParams = {
  courseId: string;
  blockId: string;
  moduleId: string;
};

export type StepPageParams = ModulePageParams & {
  stepId: string;
};

export type EducationStep = {
  id: string;
  description: string;
  number: number;
  title: string;
  is_approvement_needed: boolean;
  is_available: boolean;
  is_done: boolean;
  is_locked: boolean;
  is_started: boolean;
  category: string;
  length_minutes: number;
  type: string;
  next_step: {
    block_id: string;
    course_id: string;
    module_id: string;
    step_id: string;
  };
};

export type ExtendedEducationStep = Omit<EducationStep, 'is_available' | 'is_done' | 'is_started'> & {
  approved_at: string | null;
  completed_at: string | null;
  embed_type: 'video' | 'pdf' | 'form' | 'presentation' | 'image' | null;
  granted_at: string;
  is_approvement_needed: boolean;
  layout_type: 'text_video' | 'text_pdf' | 'text_form' | 'text_presentation' | 'text';
  materials_link: string;
  materials_link_download: string | null;
  message: string;
};

export type CompletedEducationStep = {
  step_id: string;
  module_id: string;
  was_previously_completed: boolean;
  completed_at: string;
};
