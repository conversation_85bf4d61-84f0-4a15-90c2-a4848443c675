import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';
import { t } from 'i18next';
import { routes } from '@/app/router';
import {
  BaseCheckLevelProgress,
  CourseLevelProgress,
  DistanceLevelProgress,
  LevelProgressMetric,
  useGetUserLevel,
} from '@/features/courses/api';
import { isApiError } from '@/shared/api';
import { AlertCard } from '@/shared/components/alert-card';
import { Box, HStack, VStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { Heading } from '@/shared/components/heading';
import { Icon } from '@/shared/components/icon';
import { Paper } from '@/shared/components/paper';
import { Skeleton } from '@/shared/components/skeleton';
import { Tag } from '@/shared/components/tag';
import { Text } from '@/shared/components/text';
import { ProgressBar } from './progress-bar';

const timeUntilEvent = (eventDate: Date): string => {
  const now = new Date();

  const days = differenceInDays(eventDate, now);
  if (days >= 1) return t('time.days', { count: days });

  const hours = differenceInHours(eventDate, now);
  if (hours >= 1) return t('time.hours', { count: hours });

  const minutes = differenceInMinutes(eventDate, now);
  return t('time.minutes', { count: minutes });
};

const Level = ({
  label,
  status,
  mark,
}: {
  label: string | number;
  status: 'locked' | 'previous' | 'in-progress' | 'success' | 'failed';
  mark?: React.ReactNode;
}) => {
  const border = {
    locked: '1px solid #FFD700',
    previous: '1px solid #9E9E9E',
    'in-progress': '1px solid #FFD700',
    success: '1px solid #3EC779',
    failed: '1px solid #DD334A',
  };

  return (
    <HStack $flexWrap="nowrap">
      {mark ??
        (status === 'success' ? (
          <Icon name="check" $size={20} $color="#0CA761" />
        ) : status === 'failed' ? (
          <Icon name="close" $size={20} $color="#DD334A" />
        ) : null)}

      <HStack $px={4} $h={8} $border={border[status]} $borderRadius="full">
        {status === 'locked' ? (
          <Box
            as="img"
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAVCAYAAABG1c6oAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMDSURBVHgBrVRNSFRRFD733jdjWuNogUWYICG2KaLatChaCAUR1KZNtIlWLYJaBLapTZAUtehPqCiEINykblq0CP9WKZKJaPhTi7RRG8f5v/+d+8aZdBqZqO7jcC/nvPe97zvfeQ/gPy9S7oYbbU/rU5QdJLRCskRk4vbNqzN/Bdj2vLs5UBl+sqkqfMzoLDHaArcMeCbxngUqLraeOzLzx4DvRqf3clXVG5n6WDs28Dom0rFeq21gV2PzoZ37T9XFTDgWEPMtl84eHy5+lhYnOjs7K7dVh3r4wkTtzFBH+/SXaMOjZ12nH7/oPjm3mGlKzfY8aKhRNV5lXVfbtQshKLf6BwfPD8/E7L221tFSdWstedh+p//NUMS+7Hh1pSzDcG3tmeWleViORu+WAiSE2PHRkfs7QhYqQltPlAW0NNAYX16Ar1NLn2CDNTW19EGmo6CU2FMWcH7++/bFSARkUM5tBNjU1JROLEchFV+pKq4VXO57e2tf/e6GvthKKpzNStBaJHiWWyklcM4hmU6Bsca/1xhDhFAhpRQQK+IyHj98+Xr3uKt5eUAufjCPhcNbKpMAKgtcq5D1NDBiQEsOMhsDBw4WfGBr3EGD4ZlqYliBqfeLbBAYC4DnVWBoZEjQUYImWMxroMwDoo2zGWgelBDn0jrJawAFNpkDz6ZB8CyGBKkcsEFmWEN2BiXi2OTCOJbI0L1E0RKAMtdSSilGfrc+S0ocUwzMQ66NBXZrUkWAAbScer40Pyj4wNZqIIwhGAbR4Ot1siEvm67TWTi6fis0Q/IMyuUgRE6y+ylIIVCuAI2SV11GUIM1rKNsAqUkg2PEVhlKNIKBM9LZyhjNtQB35647uwlyLXGyrYLSgKgL6wxHxe0YCEZxbNzZPeiu/OTagrkbuOxmbPbzLMx9W4B02rlqQKGDjqTgCpLJDGiU5xz2k/6QWwgyC5uDW34HHJnkk7NjAy3AUJhlaCql2hikhjNolU+DEs/pd9INOo9fkTABL6gPHK0fLyDaEt/zv6yfrCecvdk4aIQAAAAASUVORK5CYII="
          />
        ) : (
          <Text $m={0} $size="lg" $fontWeight={500} $color="fg-default" $whiteSpace="nowrap">
            {label}
          </Text>
        )}
      </HStack>
    </HStack>
  );
};

export const EducationProgress = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const { data, isFetching, isError, error } = useGetUserLevel();

  const metricMap = useMemo(() => {
    const labels: Record<LevelProgressMetric, string> = {
      distance: t('education.distanceLevel'),
      course: t('education.tasksLevel'),
      base_check: t('education.baseCheckLevel'),
    };

    type Data = {
      [LevelProgressMetric.Distance]: DistanceLevelProgress;
      [LevelProgressMetric.Course]: CourseLevelProgress;
      [LevelProgressMetric.BaseCheck]: BaseCheckLevelProgress;
    };

    const map = {} as Data;

    if (!data) return undefined;

    for (const key in data.progress) {
      const item = data.progress[key as keyof Data];
      const progress = (item.metric_value / item.metric_target) * 100;
      map[key as keyof Data] = { ...item, progress, label: labels[key as keyof Data] };
    }

    return data?.progress.reduce(
      (acc, item) => {
        const progress = (item.metric_value / item.metric_target) * 100;
        return { ...acc, [item.metric_name]: { ...item, progress, label: labels[item.metric_name] } };
      },
      {} as Record<LevelProgressMetric, LevelProgress & { progress: number; label: string }>,
    );
  }, [data?.progress, t]);

  console.log('data', data);
  console.log('metricMap?.base_check', metricMap?.base_check);

  // const progressByMetric = {
  //   distance: metricMap?.distance.progress ?? 0,
  //   course: metricMap?.course.progress ?? 0,
  //   base_check: metricMap?.base_check.progress ?? 0,
  // };
  // const { distance, course, base_check } = progressByMetric;

  // const tournamentsLeft = data?.distance_remaining ?? 0;
  // const tasksLeft = data?.tasks_remaining ?? 0;

  if (isError) return isApiError(error) && error.status === 404 ? null : <AlertCard $maxW={420} $flex="auto" />;

  // const isBaseLocked = !metricMap?.base_check.is_activated;
  // const isPreviousBase = !metricMap?.base_check.is_actual;
  // const isCurrentBase = !!metricMap?.base_check.is_actual;
  // const hasEnoughHands =
  //   (metricMap?.base_check.current_level_hand_count ?? 0) >= (metricMap?.base_check.minimum_hand_count ?? 0);
  // const isCoursesCompleted = (metricMap?.course.progress ?? 0) >= 100;
  // const isTournamentsCompleted = (metricMap?.distance.progress ?? 0) >= 50;
  // const isBaseCompleted = (metricMap?.base_check.progress ?? 0) >= 50;

  const getBaseCheckMessage = () => {
    return t('education.baseCheckNotEnoughData');
  };

  return (
    <VStack $maxW={420} $gap={4} $alignItems="stretch" $flex="auto" $mt={4}>
      {isFetching ? (
        <>
          <Skeleton $h={32} />
          <Skeleton $h={56} />
        </>
      ) : (
        <>
          <Paper $p={6}>
            <HStack $justifyContent="space-between">
              <Heading $fontWeight={500}>{t('statistics.baseEvaluation')}</Heading>
              <Level label={`${metricMap?.base_check.progress ?? 0}%`} status="previous" />
            </HStack>

            <Text $m={0} $mt={4} $size="sm" $color="fg-soft-color">
              {getBaseCheckMessage()}
            </Text>

            <Box $mt={6}>
              <Tag>
                {metricMap?.base_check.next_update_date
                  ? `${t('global.updateIn')} ${timeUntilEvent(new Date(metricMap?.base_check.next_update_date))}`
                  : ''}
              </Tag>
            </Box>
          </Paper>

          <Paper $p={6}>
            {/* header */}
            <HStack $gap={4} $justifyContent="space-between" $mb={3}>
              <Heading $fontWeight={500}>{t('global.progress')}</Heading>

              <Level
                mark={
                  <Text as="span" $size="sm" $color="fg-default">
                    {t('global.lvl')}
                  </Text>
                }
                label={data?.level ?? 0}
                status="in-progress"
              />
            </HStack>

            {/* TODO */}
            {/* description */}
            <Text $m={0} $size="sm" $color="fg-soft-color">
              {t('education.levelUpMessage')}
            </Text>

            {/* progress */}
            <VStack $mt={8} $gap={4} $alignItems="stretch">
              {(['distance', 'course'] as LevelProgressMetric[]).map((metric) => {
                const item = metricMap?.[metric];
                if (!item) return null;

                const isLocked = !item.is_activated;
                const progress = isLocked ? 0 : (item.metric_value / item.metric_target) * 100;
                const isEmpty = progress === 0;
                const isCompleted = progress >= 100;

                return (
                  <HStack key={item.metric_name} $h={5}>
                    <Box $flex={1}>
                      <Text as="span" $size="sm" $color={isLocked ? 'fg-soft-color' : 'fg-default'}>
                        {item.label}
                      </Text>
                    </Box>

                    <Box $flex={1.5}>
                      <ProgressBar status={isCompleted ? 'success' : 'warning'} progress={progress} />
                    </Box>

                    <HStack $flex="none" $w={16} $h={5} $justifyContent="end">
                      <Text as="span" $size="sm" $color={isEmpty ? 'fg-soft-color' : 'fg-default'}>
                        {isLocked ? (
                          '...'
                        ) : (
                          <>
                            {item.metric_value}
                            {item.metric_type === 'percent' ? '%' : ''}
                          </>
                        )}
                      </Text>

                      {isLocked ? (
                        <Icon name="lock" $size={16} $color="fg-soft-color" />
                      ) : isCompleted ? (
                        <Icon name="check" $size={16} $color="#0CA761" />
                      ) : (
                        <Icon name="clock" $size={16} $color="fg-soft-color" />
                      )}
                    </HStack>
                  </HStack>
                );
              })}

              {/* TODO */}
              {/* next level up */}
              {/* {data && (
          <Text $m={0} $size="sm" $color="fg-soft-color">
            {generalMessage}
          </Text>
        )} */}
            </VStack>

            <HStack $mt={8}>
              {data?.current_course_step && (
                <Button
                  $variant="filled"
                  $fullWidth
                  onClick={() => {
                    const { course_id, block_id, module_id, step_id } = data.current_course_step!;
                    return navigate(
                      routes.courseEducationBlockModuleStepById({
                        courseId: course_id,
                        blockId: block_id,
                        moduleId: module_id,
                        stepId: step_id,
                      }),
                    );
                  }}
                >
                  {t('education.goToTask')}
                </Button>
              )}

              <Button $fullWidth onClick={() => navigate(routes.tournaments())}>
                {t('global.play')}
              </Button>
            </HStack>
          </Paper>
        </>
      )}
    </VStack>
  );
};
