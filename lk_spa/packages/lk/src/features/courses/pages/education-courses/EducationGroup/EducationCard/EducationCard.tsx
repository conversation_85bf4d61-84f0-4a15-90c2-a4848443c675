import { clsx } from 'clsx';
import { routes } from '@/app/router';
import type { CourseItem } from '@/features/courses/types';
import { CountInfo } from './CountInfo/CountInfo';
import { EducationCardButtons } from './EducationCardButtons/EducationCardButtons';
import styles from './EducationCard.module.scss';

type Props = {
  course: CourseItem;
};

export const EducationCard = ({ course }: Props) => {
  const {
    title,
    description,
    total_length_minutes,
    number,
    image_url,
    is_available,
    is_done,
    tasks_done,
    id,
    blocks,
    modules_passed,
    total_modules_count,
  } = course;

  const link =
    course.next_step
      ? routes.courseEducationBlockModuleStepById({
          courseId: id,
          blockId: course.next_step.block_id,
          moduleId: course.next_step.module_id,
          stepId: course.next_step.step_id,
        })
      : null;

  const isModuleInProgress = is_available && !is_done && tasks_done;
  const progress = Math.round((modules_passed / total_modules_count) * 100);

  return (
    <div className={styles.card}>
      <img src={image_url} alt={title} className={clsx(styles.img, !is_available && styles.grayscale)} />
      <div className={styles.cardItem}>
        {!!isModuleInProgress && <div style={{ width: `${progress}%` }} className={styles.progress} />}

        <div className={clsx(styles.cardInfo, !is_available && styles.opacityGrey)}>
          <div className={clsx(styles.title, is_done && styles.grey400)}>{title}</div>
          <div className={styles.description}>{description}</div>
          <CountInfo
            className={is_done ? styles.grey400 : undefined}
            tasks_total={total_modules_count}
            lengthMinutes={total_length_minutes}
          />
        </div>

        <EducationCardButtons
          link={link}
          is_available={is_available}
          is_done={is_done}
          tasks_done={tasks_done}
          number={number}
          progress={progress}
        />
      </div>
    </div>
  );
};
