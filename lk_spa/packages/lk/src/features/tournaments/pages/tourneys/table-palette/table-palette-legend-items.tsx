import { Color } from 'styled-components';
import { Box, HStack } from '@/shared/components/box';
import { Text } from '@/shared/components/text';
import { Palette } from './table-palette.context';
import styles from './table-palette.module.scss';

interface Props {
  palette: Palette;
  $textColor?: Color;
}

export const TablePaletteLegendItems = ({ palette, $textColor = 'fg-muted' }: Props) => {
  const paletteMap = {
    top: 'dark_green',
    high: 'light_green',
    mid: 'yellow',
    low: 'red',
  };

  return (
    <HStack className={styles[`palette-variant-${palette}`]} $justifyContent="space-between">
      {Object.keys(paletteMap).map((level) => {
        const color = paletteMap[level as keyof typeof paletteMap];
        return (
          <HStack key={level}>
            <Box
              $w={5}
              $h={5}
              $borderRadius="xs"
              $bgColor="var(--palette-color)"
              className={styles[`palette--${color}`]}
            />
            <Text $m={0} $textTransform="capitalize" $color={$textColor}>
              {level}
            </Text>
          </HStack>
        );
      })}
    </HStack>
  );
};
