import { createContext, useContext } from 'react';
import { FilterParams } from '@/features/tournaments/models';

export interface TableFiltersContextValue {
  hiddenTournamentsCount?: number;
  setHiddenTournamentsCount: (count: number) => void;

  filterParams: FilterParams;
  setFilterParams: (params: FilterParams) => void;
  clearFilterParams: () => void;

  orderedBy: [string, 'asc' | 'desc'];
  setOrderedBy: (orderedBy: [string, 'asc' | 'desc']) => void;
}

export const TableFiltersContext = createContext<TableFiltersContextValue>({
  hiddenTournamentsCount: undefined,
  setHiddenTournamentsCount: () => {},

  filterParams: {},
  setFilterParams: () => {},
  clearFilterParams: () => {},

  orderedBy: ['start_date', 'asc'],
  setOrderedBy: () => {},
});

export const useTableFilters = () => useContext(TableFiltersContext);
