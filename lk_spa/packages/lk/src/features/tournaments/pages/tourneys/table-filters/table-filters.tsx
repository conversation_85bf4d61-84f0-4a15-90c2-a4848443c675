import { useCallback, useState,useMemo, useEffect } from 'react';
import { useTheme } from 'styled-components';
import { FilterParams } from '@/features/tournaments/models';
import { Box } from '@/shared/components/box';
import { Paper } from '@/shared/components/paper';
import { useLocalStorageState } from '@/shared/hooks/use-local-storage-state';
import { useSynchronizedRef } from '@/shared/hooks/use-synchronized-ref';
import { FilterControls } from './filter-controls';
import { FilterTemplates } from './filter-templates';
import { TableFiltersContext, TableFiltersContextValue } from './table-filters.context';
import { newDate } from 'react-datepicker/dist/date_utils';

const LOCAL_STORAGE_FILTERS_KEY = 'tournament:filters';

const LOCAL_STORAGE_ORDERED_BY_KEY = 'tournament:ordered-by';

export const TableFilters = ({ children }: React.PropsWithChildren) => {
  const [hiddenTournamentsCount, setHiddenTournamentsCount] = useState<number>();

  // fix valus with useMemo
  const defaultFilterParams: FilterParams = useMemo(
    () => ({
      date_start: new Date().getTime(),
      date_end: new Date().getTime() + 1000 * 60 * 60 * 12, // 12 hours
      show_hidden: false,
      show_passed: false,
      is_series: '',
      rooms_id: [],
      tourney_type: [],
      bi_start: '',
    }),
    [],
  );

  const defaultFilterParamsRef = useSynchronizedRef(defaultFilterParams);

  const [filterParams, setFilterParams] = useLocalStorageState<FilterParams>(
    LOCAL_STORAGE_FILTERS_KEY,
    defaultFilterParams,
    {
      deserialize: (value) => {
        // here we just reset date range to default
        // to fix problem when the user open tournaments page after a long time (next day for example)
        try {
          const params = JSON.parse(value) as FilterParams;
          params.date_start = defaultFilterParams.date_start;
          params.date_end = defaultFilterParams.date_end;
          return params;
        } catch (error) {
          console.error('Error parsing filter params:', error);
          return defaultFilterParams;
        }
      },
    },
  );

  const [orderedBy, setOrderedBy] = useLocalStorageState<TableFiltersContextValue['orderedBy']>(
    LOCAL_STORAGE_ORDERED_BY_KEY,
    ['start_date', 'asc'],
  );

  const value: TableFiltersContextValue = {
    hiddenTournamentsCount,
    setHiddenTournamentsCount,

    filterParams,
    setFilterParams: useCallback(
      (params) => setFilterParams({ ...defaultFilterParamsRef.current, ...params }), //
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [],
    ),
    clearFilterParams: useCallback(
      () => setFilterParams(defaultFilterParamsRef.current), //
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [],
    ),

    orderedBy,
    setOrderedBy,
  };

  const theme = useTheme();

  useEffect(() => {
    const now = Date.now();
    setFilterParams(prev => ({
      ...prev,
      date_start: now,
      // если нужно поддерживать окно в 12 часов от "сейчас":
      date_end: now + 12 * 60 * 60 * 1000,
    }));
  }, []);

  return (
    <TableFiltersContext.Provider value={value}>
      <Paper $p={0} $px={6} $pt={2} $pb={4} $mb={2}>
        <FilterTemplates />
        <Box $borderTop={`1px solid ${theme.color['border-muted-color']}`} $mb={4} $mx={-12} />
        <FilterControls />
      </Paper>

      {children}
    </TableFiltersContext.Provider>
  );
};
