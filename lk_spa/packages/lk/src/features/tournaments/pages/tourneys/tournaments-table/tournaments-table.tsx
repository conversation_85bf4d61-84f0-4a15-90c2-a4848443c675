import { useRef } from 'react';
import { AgGridReact } from 'ag-grid-react';
import clsx from 'clsx';
import { TableFilters } from '../table-filters';
import { useTablePalette } from '../table-palette';
import { TournamentSelection } from './components/tournament-selection';
import { AllTournamentsTable } from './sub-tables/all-tournaments';
import { SelectedTournamentsTable } from './sub-tables/selected-tournaments';
import styles from '../table-palette/table-palette.module.scss';

// TODO: problems with data fetching and revalidation
// TODO: issue with page swithing and colorizing cells of first column (add custom classname to cell or row?)
// TODO: virtualization

export const TournamentsTable = () => {
  const { palette } = useTablePalette();

  const gridOne = useRef<AgGridReact>(null);
  const gridTwo = useRef<AgGridReact>(null);

  return (
    <TableFilters>
      <TournamentSelection>
        <div className={clsx(styles.wrapper, styles[`palette-variant-${palette}`])}>
          <SelectedTournamentsTable gridRef={gridOne} alignedGrids={[gridTwo]} />
          <AllTournamentsTable gridRef={gridTwo} />
        </div>
      </TournamentSelection>
    </TableFilters>
  );
};
