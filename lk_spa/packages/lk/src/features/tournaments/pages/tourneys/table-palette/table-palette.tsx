import { useTranslation } from 'react-i18next';
import { Box, HStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { Heading } from '@/shared/components/heading';
import { CompactPopover } from '@/shared/components/popover';
import { RadioGroup, RadioItem } from '@/shared/components/radio';
import { Switch } from '@/shared/components/switch';
import { TableLegendFloatPanel } from './table-legend-float-panel';
import { TablePaletteLegendItems } from './table-palette-legend-items';
import { Palette, useTablePalette } from './table-palette.context';

export const TablePalette = () => {
  const { t } = useTranslation();
  const { palette, setPalette, isLegendPanelOpen, toggleLegendPanel } = useTablePalette();

  const trigger = (
    <Button $icon="palette" $colorScheme="neutral">
      {t('tournaments.setupLegend')}
    </Button>
  );

  const palettes = [Palette.One, Palette.Two, Palette.Three, Palette.Four];

  return (
    <>
      <CompactPopover trigger={trigger}>
        <Box $p={4} $w="container-xs">
          <HStack $justifyContent="space-between" $mb={4}>
            <Heading $size="lg">{t('global.legend')}</Heading>
            <Switch checked={isLegendPanelOpen} onChange={toggleLegendPanel} />
          </HStack>

          <RadioGroup value={palette} direction="column">
            {palettes.map((p) => {
              const handleClick = () => setPalette(p);
              return (
                <Box
                  key={p}
                  $px={4}
                  $py={3}
                  $w="100%"
                  $bgColor="bg-surface-3"
                  $borderRadius="lg"
                  $cursor="pointer"
                  onClick={handleClick}
                >
                  <HStack $justifyContent="space-between" $mb={3.5}>
                    <Heading $size="md" $fontWeight={400}>{`${t('global.palette')} ${p}`}</Heading>
                    <RadioItem value={p}>{p}</RadioItem>
                  </HStack>

                  <TablePaletteLegendItems palette={p} />
                </Box>
              );
            })}
          </RadioGroup>
        </Box>
      </CompactPopover>

      <TableLegendFloatPanel />
    </>
  );
};
