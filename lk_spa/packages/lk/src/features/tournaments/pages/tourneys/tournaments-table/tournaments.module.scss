.wrapper {
  // border radius for first table
  :global(.will-play-table .ag-root-wrapper) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  // border radius for second table
  :global(.all-tournaments-table .ag-root-wrapper) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  // min height for tables (loading state)
  :global(.ag-center-cols-viewport),
  :global(.ag-center-cols-container) {
    min-height: 144px;
  }

  // header separator when reordering
  :global(.ag-header .ag-header-cell.ag-header-highlight-after::after),
  :global(.ag-header .ag-header-cell.ag-header-highlight-before::after) {
    right: 4px;
    width: 2px;
    height: 36px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.noContent {
  :global(.ag-body) {
    visibility: hidden;
    height: 0;
  }
  :global(.ag-header) {
    border-bottom: 0;
  }
}

.headerCentered {
  :global(.ag-header-cell-label) {
    justify-content: center;
  }
}

.palette {
  // row marker
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background-color: var(--palette-color);
  }

  // cells
  :global(.ag-cell[aria-colindex='1']),
  :global(.ag-cell[aria-colindex='2']),
  :global(.ag-cell[aria-colindex='3']) {
    color: var(--fg-default);
    border: 0;

    &::before {
      content: '';
      position: absolute;
      z-index: -1;
      inset: 0;
      opacity: 0.5;
      background-color: var(--palette-color);
    }
  }

  // apply gradient for name column
  :global(.ag-cell[col-id='name'][aria-colindex='3']::before) {
    background: linear-gradient(90deg, var(--palette-color) 0%, transparent var(--gradient-size));
  }

  --gradient-size: 66%;
}
