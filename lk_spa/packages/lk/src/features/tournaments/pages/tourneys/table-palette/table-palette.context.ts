import { createContext, useContext } from 'react';

export enum Palette {
  One = '1',
  Two = '2',
  Three = '3',
  Four = '4',
}

export interface TablePaletteContextValue {
  palette: Palette;
  setPalette: (palette: Palette) => void;

  isLegendPanelOpen: boolean;
  toggleLegendPanel: () => void;
}

export const TablePaletteContext = createContext<TablePaletteContextValue>({
  palette: Palette.One,
  setPalette: () => {},

  isLegendPanelOpen: true,
  toggleLegendPanel: () => {},
});

export const useTablePalette = () => useContext(TablePaletteContext);
