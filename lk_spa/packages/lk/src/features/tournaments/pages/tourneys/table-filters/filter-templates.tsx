import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { styled } from 'styled-components';
import { FilterTemplate, useGetFilterTemplates } from '@/features/tournaments/api';
import { HStack } from '@/shared/components/box';
import { Icon } from '@/shared/components/icon';
import { Spinner } from '@/shared/components/spinner';
import { useLocalStorageState } from '@/shared/hooks/use-local-storage-state';
import { rem } from '@/theme';
import { FilterTemplateEditor } from './filter-template-editor';
import { useTableFilters } from './table-filters.context';

const StyledButton = styled.button.attrs({ type: 'button' })<{ $active?: boolean; $static?: boolean }>(
  ({ theme, $active, $static }) => ({
    all: 'unset',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    fontFamily: theme.fontFamily.primary,
    ...theme.font.sm,
    fontWeight: 500,
    padding: `0 ${theme.spacing[6]}`,
    color: theme.color['fg-soft-color'],
    transition: 'all 0.15s ease-in-out',
    boxSizing: 'border-box',
    borderBottom: $active ? `2px solid ${theme.color['accent']}` : undefined,
    height: theme.spacing[11],

    '& > .edit-icon': {
      transition: 'all 0.15s ease-in-out',
      opacity: 0,
      width: 0,
      marginLeft: 0,
      padding: 0,
      position: 'relative',

      '&::after': {
        content: '""',
        position: 'absolute',
        top: '-' + theme.spacing[1], // negative
        right: '-' + theme.spacing[1],
        width: theme.spacing[7],
        height: theme.spacing[8],
      },
    },

    '&:hover': {
      color: theme.color['fg-default'],
      padding: $static ? undefined : `0 ${theme.spacing[2]} 0 ${theme.spacing[6]}`,

      '& > .edit-icon': {
        opacity: 1,
        width: rem(20),
        marginLeft: theme.spacing[3],
        padding: theme.spacing[0.5],
      },
    },

    '& > .plus-icon': {
      marginRight: theme.spacing[1.5],
    },
  }),
);

const LOCAL_STORAGE_ACTIVE_FILTER_KEY = 'tournament:active-filter';

export const FilterTemplates = () => {
  const { t } = useTranslation();

  const [activeFilter, setActiveFilter] = useLocalStorageState<number | null>(
    LOCAL_STORAGE_ACTIVE_FILTER_KEY,
    null,
  );

  const [filterToEdit, setFilterToEdit] = useState<FilterTemplate | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);

  const { setFilterParams, clearFilterParams } = useTableFilters();

  const { data: filters, isLoading } = useGetFilterTemplates();

  const addTemplate = () => {
    setFilterToEdit(null);
    setIsEditorOpen(true);
  };

  const resetFilters = () => {
    setActiveFilter(null);
    setFilterToEdit(null);
    clearFilterParams();
  };

  return (
    <>
      <FilterTemplateEditor //
        template={filterToEdit}
        isOpen={isEditorOpen}
        setIsOpen={setIsEditorOpen}
      />

      <HStack $justifyContent="start" $flexWrap="wrap">
        {/* all filters */}
        <StyledButton $active={activeFilter === null} $static onClick={resetFilters}>
          {t('global.allFilters')}
        </StyledButton>

        {/* user filters */}
        {isLoading ? (
          <HStack $px={4}>
            <Spinner />
          </HStack>
        ) : (
          filters?.map((filter) => {
            const selectFilter = () => {
              setActiveFilter(filter.id);
              setFilterParams(filter.value);
            };
            const editFilter = (event: React.MouseEvent<HTMLDivElement>) => {
              event.stopPropagation();
              setFilterToEdit(filter);
              setIsEditorOpen(true);
            };
            return (
              <StyledButton key={filter.id} $active={activeFilter === filter.id} onClick={selectFilter}>
                <span>{filter.name}</span>
                <span className="edit-icon" onClick={editFilter} children={<Icon name="pencil" $size={16} />} />
              </StyledButton>
            );
          })
        )}

        {/* add new filter */}
        <StyledButton $static onClick={addTemplate}>
          <Icon name="plus" $size={20} className="plus-icon" />
          <span>{t('global.add')}</span>
        </StyledButton>
      </HStack>
    </>
  );
};
