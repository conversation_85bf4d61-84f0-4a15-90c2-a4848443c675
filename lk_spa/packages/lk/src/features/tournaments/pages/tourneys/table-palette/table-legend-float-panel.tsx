import { useDebounce } from 'use-debounce';
import { Box } from '@/shared/components/box';
import { Fade } from '@/shared/components/fade';
import { Paper } from '@/shared/components/paper';
import { TablePaletteLegendItems } from './table-palette-legend-items';
import { useTablePalette } from './table-palette.context';

export const TableLegendFloatPanel = () => {
  const { isLegendPanelOpen, palette } = useTablePalette();

  const [debouncedIsLegendPanelOpen] = useDebounce(isLegendPanelOpen, 200);
  const isUnmounted = !isLegendPanelOpen && !debouncedIsLegendPanelOpen;

  if (isUnmounted) return null;

  return (
    <Box $position="fixed" $bottom={8} $right={8} $zIndex="sticky">
      <Fade $in={isLegendPanelOpen}>
        <Paper $p={6} $py={5} $bgColor="bg-surface-2" $w="container-xs" $shadow="0 2px 16px 0 #00000050">
          <TablePaletteLegendItems palette={palette} $textColor="fg-default" />
        </Paper>
      </Fade>
    </Box>
  );
};
