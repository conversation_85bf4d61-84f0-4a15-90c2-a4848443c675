import { useTranslation } from 'react-i18next';
import { Button, HStack } from '@/shared/components';
import { GuideDialog } from '@/shared/guides';
import { TournamentsHeader } from '../../components/tournaments-header';
import { TableColumnsPicker, TableColumnsProvider } from './table-columns';
import { TablePalette, TablePaletteProvider } from './table-palette';
import { TournamentsTable } from './tournaments-table';

export const Tourneys = () => {
  const { t } = useTranslation();

  return (
    <>
      {/* <PageHeader children={t('navMenu.tournaments')} /> */}

      <TournamentsHeader />

      <TablePaletteProvider>
        <TableColumnsProvider>
          <HStack $gap={2} $position="absolute" $top={6} $right={10}>
            <GuideDialog
              guide="tournaments"
              trigger={
                // button is required for passing trigger's ref
                <Button $colorScheme="neutral" $icon="question-mark">
                  {t('guides.tournaments')}
                </Button>
              }
            />
            <TablePalette />
            <TableColumnsPicker />
          </HStack>

          <TournamentsTable />
        </TableColumnsProvider>
      </TablePaletteProvider>
    </>
  );
};
