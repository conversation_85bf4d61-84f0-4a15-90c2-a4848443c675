import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetRooms, useGetTourneyTypes } from '@/features/tournaments/api';
import { HStack } from '@/shared/components/box';
import { CheckboxFormControl } from '@/shared/components/checkbox';
import { DatePicker } from '@/shared/components/date-picker';
import { FormControl, Label } from '@/shared/components/form-control';
import { TextInput } from '@/shared/components/input';
import { MultiSelect } from '@/shared/components/multi-select';
import { sanitizeDecimalInput } from '@/shared/utils/input';
import { useTableFilters } from './table-filters.context';

export const FilterControls = () => {
  const { t } = useTranslation();

  const { filterParams, setFilterParams } = useTableFilters();

  const { data: rooms, isLoading: isRoomsLoading, isError: isRoomsError } = useGetRooms();
  const roomsOptions = useMemo(
    () => rooms?.map((room) => ({ label: room.abrname, value: room.id })) || [],
    [rooms],
  );

  const {
    data: tourneyTypes,
    isLoading: isTourneyTypesLoading,
    isError: isTourneyTypesError,
  } = useGetTourneyTypes();
  const tourneyTypesOptions = useMemo(
    () => tourneyTypes?.map((type) => ({ label: type.label, value: type.value })) || [],
    [tourneyTypes],
  );

  const seriesOptions = [
    { label: 'Regular', value: 'false' },
    { label: 'Series', value: 'true' },
  ];

  return (
    <HStack $justifyContent="start" $gap={3} $flexWrap="wrap">
      {/* rooms select */}
      <FormControl>
        <Label>{t('global.rooms')}</Label>
        <MultiSelect
          value={filterParams.rooms_id}
          onChange={(event) => {
            setFilterParams({ ...filterParams, rooms_id: event.target.value.split(',').filter(Boolean) });
          }}
          options={roomsOptions}
          placeholder={isRoomsError ? 'Error' : t('global.rooms')}
          isLoading={isRoomsLoading}
          width={160}
        />
      </FormControl>

      {/* start date */}
      <FormControl $w={56} $flex="none">
        <Label>{t('education.from')}</Label>
        <DatePicker
          value={filterParams.date_start}
          onChange={(date) => {
            setFilterParams({ ...filterParams, date_start: date as number });
          }}
          showTimeInput
          dateFormat="dd.MM.yyyy HH:mm"
        />
      </FormControl>

      {/* end date */}
      <FormControl $w={56} $flex="none">
        <Label>{t('tournaments.to')}</Label>
        <DatePicker
          value={filterParams.date_end}
          onChange={(date) => {
            setFilterParams({ ...filterParams, date_end: date as number });
          }}
          showTimeInput
          dateFormat="dd.MM.yyyy HH:mm"
        />
      </FormControl>

      {/* tourney type */}
      <FormControl>
        <Label>{t('global.tourneyType')}</Label>
        <MultiSelect
          value={filterParams.tourney_type}
          onChange={(event) => {
            setFilterParams({ ...filterParams, tourney_type: event.target.value.split(',').filter(Boolean) });
          }}
          options={tourneyTypesOptions}
          placeholder={isTourneyTypesError ? 'Error' : t('global.tourneyType')}
          isLoading={isTourneyTypesLoading}
          width={176}
        />
      </FormControl>

      {/* series */}
      <FormControl>
        <Label>{t('tournaments.series')}</Label>
        <MultiSelect
          value={filterParams.is_series}
          onChange={(event) => {
            setFilterParams({ ...filterParams, is_series: event.target.value });
          }}
          options={seriesOptions}
          placeholder={t('tournaments.series')}
          width={176}
        />
      </FormControl>

      {/* min buy-in */}
      <FormControl $w={44} $flex="none">
        <Label>{t('tournaments.minBuyin')}</Label>
        <TextInput
          value={filterParams.bi_start || ''} // avoid console error
          onChange={(event) => {
            sanitizeDecimalInput(event);
            setFilterParams({ ...filterParams, bi_start: event.target.value });
          }}
          placeholder={t('tournaments.minBuyin')}
        />
      </FormControl>

      <HStack $pt={5} $px={1} $gap={4}>
        {/* hidden tournaments */}
        <CheckboxFormControl
          label={t('tournaments.hidden')}
          value={filterParams.show_hidden}
          onChange={(checked) => {
            setFilterParams({ ...filterParams, show_hidden: checked });
          }}
        />

        {/* past tournaments */}
        <CheckboxFormControl
          label={t('tournaments.past')}
          value={filterParams.show_passed}
          onChange={(checked) => {
            setFilterParams({ ...filterParams, show_passed: checked });
          }}
        />
      </HStack>
    </HStack>
  );
};
