import { useState } from 'react';
import { useLocalStorageState } from '@/shared/hooks/use-local-storage-state';
import { Palette, TablePaletteContext, TablePaletteContextValue } from './table-palette.context';

const LOCAL_STORAGE_PALETTE_KEY = 'tournament:palette';

export const TablePaletteProvider = ({ children }: React.PropsWithChildren) => {
  const [palette, setPalette] = useLocalStorageState<Palette>(LOCAL_STORAGE_PALETTE_KEY, Palette.One);
  const [isLegendPanelOpen, setIsLegendPanelOpen] = useState(true);

  const value: TablePaletteContextValue = {
    palette,
    setPalette,

    isLegendPanelOpen,
    toggleLegendPanel: () => setIsLegendPanelOpen((prev) => !prev),
  };

  return <TablePaletteContext.Provider value={value}>{children}</TablePaletteContext.Provider>;
};
