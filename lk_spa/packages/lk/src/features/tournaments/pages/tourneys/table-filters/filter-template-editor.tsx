import { useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  FilterTemplate,
  useCreateFilterTemplate,
  useDeleteFilterTemplate,
  useGetRooms,
  useGetTourneyTypes,
  useUpdateFilterTemplate,
} from '@/features/tournaments/api';
import { HStack, VStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { Dialog } from '@/shared/components/dialog';
import { ErrorMessage, FormControl, Label } from '@/shared/components/form-control';
import { TextInput } from '@/shared/components/input';
import { MultiSelect } from '@/shared/components/multi-select';
import { Text } from '@/shared/components/text';
import { useToast } from '@/shared/components/toast';
import { sanitizeDecimalInput } from '@/shared/utils/input';

type FormValues = {
  name: string;
  rooms_id: (string | number)[];
  bi_start?: string;
  tourney_type: (string | number)[];
};

const defaultValues: FormValues = {
  name: '',
  rooms_id: [],
  bi_start: '',
  tourney_type: [],
};

type Props = {
  template?: FilterTemplate | null;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
};

export const FilterTemplateEditor = ({ template, isOpen, setIsOpen }: Props) => {
  const isEdit = !!template;

  const { t } = useTranslation();
  const toast = useToast();
  // const confirmation = useConfirmation();

  const { data: rooms, isLoading: isRoomsLoading, isError: isRoomsError } = useGetRooms();
  const roomsOptions = useMemo(
    () => rooms?.map((room) => ({ label: room.abrname, value: room.id })) || [],
    [rooms],
  );

  const {
    data: tourneyTypes,
    isLoading: isTourneyTypesLoading,
    isError: isTourneyTypesError,
  } = useGetTourneyTypes();
  const tourneyTypesOptions = useMemo(
    () => tourneyTypes?.map((type) => ({ label: type.label, value: type.value })) || [],
    [tourneyTypes],
  );

  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormValues>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues,
  });

  useEffect(() => {
    if (!isOpen) return;
    reset(template ? { name: template.name, ...template.value } : defaultValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const { mutateAsync: createFilterTemplate } = useCreateFilterTemplate();
  const { mutateAsync: updateFilterTemplate } = useUpdateFilterTemplate();
  const { mutateAsync: deleteFilterTemplate, isPending: isDeletePending } = useDeleteFilterTemplate();

  const submit = handleSubmit(async (values) => {
    try {
      const { name, ...value } = values;
      if (isEdit) {
        await updateFilterTemplate({ id: template.id, name, value });
        toast.success({ title: t('global.updated') });
      } else {
        await createFilterTemplate({ name, value });
        toast.success({ title: t('global.done') });
      }
      setIsOpen(false);
    } catch (error) {
      toast.fetchError(error);
    }
  });

  const remove = async () => {
    /* TODO: add confirmation */
    // TODO: confirmation not working because of positioning of modals

    if (!template) return;
    try {
      await deleteFilterTemplate(template.id);
      setIsOpen(false);
      toast.success({ title: t('global.deleted') });
    } catch (error) {
      toast.fetchError(error);
    }
  };

  // const remove = () => {
  //   confirmation({
  //     title: t('global.areYouSure'),
  //     body: t('global.cantUndo'),
  //     onConfirm: async () => {
  //       if (!template) return;
  //       try {
  //         await deleteFilterTemplate(template.id);
  //         setIsOpen(false);
  //         toast.success({ title: t('global.deleted') });
  //       } catch (error) {
  //         toast.fetchError(error);
  //       }
  //     },
  //   });
  // };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={setIsOpen}
      title={t(isEdit ? 'tournaments.editTemplate' : 'tournaments.addTemplate')}
      content={
        <form onSubmit={submit} noValidate>
          <FormControl invalid={!!errors.name}>
            <Label hasAsterisk>{t('tournaments.templateName')}</Label>
            <TextInput
              {...register('name', {
                required: t('global.requiredField'),
              })}
              placeholder={t('tournaments.templateName')}
            />
            <ErrorMessage>{errors.name?.message}</ErrorMessage>
          </FormControl>

          <Text $size="sm" $mt={5} $mb={4}>
            {t('tournaments.setValuesForTemplate')}
          </Text>

          <VStack $alignItems="stretch" $gap={5}>
            <Controller
              control={control}
              name="rooms_id"
              render={({ field }) => (
                <FormControl invalid={!!errors.rooms_id}>
                  <Label>{t('global.rooms')}</Label>
                  <MultiSelect
                    {...field}
                    onChange={(event) => {
                      field.onChange(event.target.value.split(',').filter(Boolean));
                    }}
                    options={roomsOptions}
                    placeholder={isRoomsError ? 'Error' : t('global.rooms')}
                    isLoading={isRoomsLoading}
                    fullWidth
                    asModal // ???
                  />
                  <ErrorMessage>{errors.rooms_id?.message}</ErrorMessage>
                </FormControl>
              )}
            />

            <Controller
              control={control}
              name="bi_start"
              render={({ field }) => {
                const handleChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
                  sanitizeDecimalInput(event);
                  field.onChange(event);
                };
                return (
                  <FormControl invalid={!!errors.bi_start}>
                    <Label>{t('tournaments.minBuyin')}</Label>
                    <TextInput
                      {...field}
                      value={field.value || ''} // avoid console error on editing
                      onChange={handleChange}
                      placeholder={t('tournaments.minBuyin')}
                    />
                    <ErrorMessage>{errors.bi_start?.message}</ErrorMessage>
                  </FormControl>
                );
              }}
            />

            <Controller
              control={control}
              name="tourney_type"
              render={({ field }) => (
                <FormControl invalid={!!errors.tourney_type}>
                  <Label>{t('global.tourneyType')}</Label>
                  <MultiSelect
                    {...field}
                    onChange={(event) => {
                      field.onChange(event.target.value.split(',').filter(Boolean));
                    }}
                    options={tourneyTypesOptions}
                    placeholder={isTourneyTypesError ? 'Error' : t('global.tourneyType')}
                    isLoading={isTourneyTypesLoading}
                    fullWidth
                    asModal // ???
                  />
                  <ErrorMessage>{errors.tourney_type?.message}</ErrorMessage>
                </FormControl>
              )}
            />
          </VStack>

          <HStack $justifyContent="space-between" $mt={8}>
            {isEdit && (
              <Button $colorScheme="red" onClick={remove} loading={isDeletePending}>
                {t('global.remove')}
              </Button>
            )}

            <HStack $flex={1} $justifyContent="end">
              <Button $colorScheme="neutral" onClick={() => setIsOpen(false)}>
                {t('global.cancel')}
              </Button>
              <Button type="submit" loading={isSubmitting}>
                {template ? t('global.save') : t('global.add')}
              </Button>
            </HStack>
          </HStack>
        </form>
      }
    />
  );
};
