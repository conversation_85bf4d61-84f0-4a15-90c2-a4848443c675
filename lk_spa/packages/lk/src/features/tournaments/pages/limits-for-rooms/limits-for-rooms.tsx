import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { RoomLimit, useGetTourneysLimits } from '@/features/tournaments/api';
import { Accordion, CompactAccordionItem } from '@/shared/components/accordion';
import { AlertCard } from '@/shared/components/alert-card';
import { Box, Grid, GridItem, HStack, VStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { Heading } from '@/shared/components/heading';
import { Icon, IconName } from '@/shared/components/icon';
import { Skeleton } from '@/shared/components/skeleton';
import { Text } from '@/shared/components/text';
import { Tooltip } from '@/shared/components/tooltip';
import { TournamentsHeader } from '../../components/tournaments-header';

export const LimitsForRooms = () => {
  const { t } = useTranslation();

  const { data, isLoading, isError } = useGetTourneysLimits();

  const itemsMappedByRoom = useMemo(
    () =>
      data?.reduce(
        (acc, item) => {
          if (!acc[item.room]) acc[item.room] = [];
          acc[item.room].push(item);
          return acc;
        },
        {} as Record<string, RoomLimit[]>,
      ),
    [data],
  );

  const rooms = Object.keys(itemsMappedByRoom ?? {});

  const mappedRoomData: Record<string, { name: string; icon: IconName }> = {
    'PD ₽': { name: 'PD ₽', icon: 'poker-dom' },
    'PP $': { name: 'PokerPlanets', icon: 'poker-planets' },
    CoinPoker: { name: 'CoinPoker', icon: 'coin-poker' },
    'Mobile Apps': { name: 'Mobile Apps', icon: 'mobile' },
    Другое: { name: 'Другое', icon: 'grid' },
  };

  const mappedTournamentName: Record<string, string> = {
    ko: t('global.ko'),
    freeze_out: t('tournaments.freezeOut'),
    ko_rebuy: t('tournaments.koRebuy'),
    freeze_out_rebuy: t('tournaments.freezeOutRebuy'),
  };

  const mappedRoomTypeDefinition: Record<string, string> = {
    reg6: t('tournaments.regspeed6maxDefinition'),
    reg9: t('tournaments.regspeedFullringDefinition'),
    turbo6: t('tournaments.turbo6maxDefinition'),
    turbo9: t('tournaments.turboFullringDefinition'),
  };

  const renderLabel = (label: string) => {
    return <Text $m={0} $color="fg-muted" $fontWeight={500} children={label} />;
  };
  const renderValue = (value: string) => {
    return <Text $m={0} $size="lg" children={value} />;
  };

  const renterTooltip = (definition: string, side?: 'right' | 'left') => {
    return (
      <Tooltip
        trigger={
          <Button $variant="plain">
            <Icon name="filled-warn" $color="fg-muted" />
          </Button>
        }
        content={<Box $w={80}>{definition}</Box>}
        side={side}
      />
    );
  };

  const accordionContentHeader = (
    <Grid>
      <GridItem $size={3} />
      <GridItem $size={2}>
        <HStack $justifyContent="start">
          <span>{renderLabel(t('tournaments.regspeed6max'))}</span>
          {renterTooltip(mappedRoomTypeDefinition.reg6)}
        </HStack>
      </GridItem>
      <GridItem $size={2}>
        <HStack $justifyContent="start">
          <span>{renderLabel(t('tournaments.regspeedFullring'))}</span>
          {renterTooltip(mappedRoomTypeDefinition.reg9)}
        </HStack>
      </GridItem>
      <GridItem $size={2}>
        <HStack $justifyContent="start">
          <span>{renderLabel(t('tournaments.turbo6max'))}</span>
          {renterTooltip(mappedRoomTypeDefinition.turbo6)}
        </HStack>
      </GridItem>
      <GridItem $size={2}>
        <HStack $justifyContent="start">
          <span>{renderLabel(t('tournaments.turboFullring'))}</span>
          {renterTooltip(mappedRoomTypeDefinition.turbo9)}
        </HStack>
      </GridItem>
    </Grid>
  );

  return (
    <>
      <TournamentsHeader />

      <Heading $fontWeight={500} $mt={2} $mb={4}>
        {t('tournaments.limitsForRooms')}
      </Heading>

      {isError ? (
        <AlertCard />
      ) : isLoading ? (
        <VStack $gap={4} $alignItems="stretch">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton $h={16} key={i} />
          ))}
        </VStack>
      ) : (
        <>
          <Accordion type="single">
            {rooms.map((room) => {
              const { name, icon } = mappedRoomData[room];
              const items = itemsMappedByRoom?.[room];

              const content = (
                <VStack $gap={5} $alignItems="stretch">
                  {accordionContentHeader}
                  {items?.map((item) => (
                    <Grid key={item.type}>
                      <GridItem $size={3}>
                        <HStack $justifyContent="start">
                          <span>{renderLabel(mappedTournamentName[item.type])}</span>

                          {/* tooltip if ko_rebuy or freeze_out_rebuy */}
                          {['ko_rebuy', 'freeze_out_rebuy'].includes(item.type) &&
                            renterTooltip(t('tournaments.rebuyDefinition'), 'right')}
                        </HStack>
                      </GridItem>
                      <GridItem $size={2}>{renderValue(item.reg6)}</GridItem>
                      <GridItem $size={2}>{renderValue(item.reg9)}</GridItem>
                      <GridItem $size={2}>{renderValue(item.turbo6)}</GridItem>
                      <GridItem $size={2}>{renderValue(item.turbo9)}</GridItem>
                    </Grid>
                  ))}
                </VStack>
              );

              return <CompactAccordionItem key={room} value={room} icon={icon} label={name} content={content} />;
            })}
          </Accordion>
        </>
      )}
    </>
  );
};
