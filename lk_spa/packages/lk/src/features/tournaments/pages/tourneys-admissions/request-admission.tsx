import { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { formatDate, subDays } from 'date-fns';
import { useDebounce } from 'use-debounce';
import {
  useGetAvailableTourneyAdmissions,
  useGetTournamentRooms,
  useRequestAdmission,
} from '@/features/tournaments/api';
import { Box, HStack, VStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { DatePicker } from '@/shared/components/date-picker';
import { Dialog } from '@/shared/components/dialog';
import { ErrorMessage, FormControl, Label } from '@/shared/components/form-control';
import { TextArea, TextInput } from '@/shared/components/input';
import { Select } from '@/shared/components/select';
import { Spinner } from '@/shared/components/spinner';
import { Text } from '@/shared/components/text';
import { useToast } from '@/shared/components/toast';
import { useHookFormMask } from '@/shared/hooks/use-hook-form-mask';

type FormValues = {
  datetime: number;
  room_id: number | string;
  bi: number;
  tourney_id: number | string;
  tourney_name: string;
  tourney_time: string;
  comment: string;
};

const defaultValues: Partial<FormValues> = {
  datetime: Date.now(),
  // room_id: 5,
  tourney_time: '12:00',
};

export const RequestAdmission = () => {
  const { t } = useTranslation();
  const toast = useToast();

  const [open, setOpen] = useState(false);

  const { data: rooms, isLoading: isRoomsLoading } = useGetTournamentRooms({ enabled: open });

  const roomsOptions = useMemo(() => {
    return rooms?.map((room) => ({ label: room.name, value: room.id, field: '' })) || [];
  }, [rooms]);

  const {
    register,
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    watch,
    setValue,
    reset,
  } = useForm<FormValues>({ mode: 'onSubmit', defaultValues });

  // reset form on open
  useEffect(() => {
    if (open) reset();
  }, [open, reset]);

  const registerWithMask = useHookFormMask(register);

  const [availableTourneysParams, setAvailableTourneysParams] = useState<{
    datetime: number;
    room_id: number;
    bi: number;
  } | null>(null);

  const arePrimaryValuesEntered = !!availableTourneysParams;

  // watch for changes in form values
  useEffect(() => {
    const subscription = watch((values, { name }) => {
      // clear tourney_id if datetime, room_id or bi changed
      if (name && ['datetime', 'room_id', 'bi'].includes(name)) {
        setValue('tourney_id', 0);
      }

      const { datetime, room_id, bi } = values as FormValues;
      const entered = !!datetime && !!room_id && !!bi;

      if (entered) {
        const dateAtNoon = new Date(new Date(datetime).setHours(12, 0, 0, 0)).getTime();
        setAvailableTourneysParams({
          datetime: dateAtNoon,
          room_id: Number(room_id),
          bi: Number(bi),
        });
      } else {
        setAvailableTourneysParams(null);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, setValue]);

  const [hasTourneyManualInput, setHasTourneyManualInput] = useState(false);

  // reset manual input if primary values are entered to show available tourneys first
  useLayoutEffect(() => {
    if (arePrimaryValuesEntered) setHasTourneyManualInput(false);
    else setValue('tourney_id', 0);
  }, [arePrimaryValuesEntered, setValue]);

  // debounce available tourneys params to avoid too many http requests
  const [deboucendAvailableTourneysParams] = useDebounce(availableTourneysParams, 500);

  const { data: availableTourneys, isLoading: isAvailableTourneysLoading } = useGetAvailableTourneyAdmissions({
    enabled: open && arePrimaryValuesEntered,
    params: deboucendAvailableTourneysParams || { datetime: 0, room_id: 0, bi: 0 },
  });

  const availableTourneysOptions = useMemo(() => {
    return availableTourneys?.map((tourney) => ({ label: `${formatDate(tourney.date, 'HH:mm')} | ${tourney.name}`, value: tourney.id, field: '' })) || [];
  }, [availableTourneys]);

  const selectedTournamentId = useWatch({ control, name: 'tourney_id' });
  const selectedTournament = useMemo(() => {
    return availableTourneys?.find((tourney) => tourney.id === Number(selectedTournamentId));
  }, [availableTourneys, selectedTournamentId]);

  const { mutateAsync: requestAdmission } = useRequestAdmission();

  const submit = handleSubmit(async (values) => {
    const { datetime, room_id, bi, tourney_name, tourney_time, comment } = values;

    const fullDate = new Date(datetime);
    const [hours, minutes] = tourney_time.split(':');
    fullDate.setHours(Number(hours), Number(minutes), 0, 0);

    try {
      await requestAdmission({
        datetime: fullDate.getTime(),
        room_id,
        bi,
        tourney_id: selectedTournament?.id,
        name: selectedTournament ? selectedTournament.name : tourney_name,
        tourney_source: selectedTournament?.source,
        comment,
      });
      toast.success({ title: t('global.done') });
      setOpen(false);
    } catch (error) {
      toast.fetchError(error);
    }
  });

  return (
    <>
      <Box $position="absolute" $top={6} $right={12}>
        <Button onClick={() => setOpen(true)}>{t('tournaments.requestAdmission')}</Button>
      </Box>

      <Dialog
        open={open}
        onOpenChange={setOpen}
        title={t('tournaments.requestAdmission')}
        content={
          <form onSubmit={submit}>
            <VStack $alignItems="stretch" $gap={4}>
              {/* date */}
              <Controller
                control={control}
                name="datetime"
                rules={{ required: t('global.requiredField') }}
                render={({ field }) => {
                  const yesterday = subDays(new Date(), 1);
                  return (
                    <FormControl invalid={!!errors.datetime}>
                      <Label>{t('global.date')}</Label>
                      <DatePicker {...field} dateFormat="dd.MM.yyyy" minDate={yesterday} />
                      <ErrorMessage>{errors.datetime?.message}</ErrorMessage>
                    </FormControl>
                  );
                }}
              />

              {/* room */}
              <Controller
                control={control}
                name="room_id"
                rules={{ required: t('global.requiredField') }}
                render={({ field }) => {
                  return (
                    <FormControl invalid={!!errors.room_id}>
                      <Label>{t('global.room')}</Label>
                      <Select {...field}
                        onChange={(option) => field.onChange(option.value)}
                        options={roomsOptions}
                        fullWidth
                        isLoading={isRoomsLoading}
                        asModal />
                      <ErrorMessage>{errors.room_id?.message}</ErrorMessage>
                    </FormControl>
                  );
                }}
              />

              {/* buyin */}
              <FormControl invalid={!!errors.bi}>
                <Label>{t('global.buyin')}</Label>
                <TextInput
                  {...registerWithMask('bi', 'numeric', {
                    required: t('global.requiredField'),
                    valueAsNumber: true,
                  })}
                  placeholder={t('global.buyin')}
                />
                <ErrorMessage>{errors.bi?.message}</ErrorMessage>
              </FormControl>

              {isAvailableTourneysLoading ? (
                <HStack $py={3}>
                  <Spinner $size={32} />
                </HStack>
              ) : !!availableTourneysOptions.length && !hasTourneyManualInput ? (
                <>
                  <HStack>
                    <Controller
                      control={control}
                      name="tourney_id"
                      rules={{ required: t('global.requiredField') }}
                      render={({ field }) => {
                        return (
                          <FormControl invalid={!!errors.tourney_id} $w="100%">
                            <Label>{t('tournaments.foundTourneys')}</Label>
                            <Select {...field}
                              onChange={(option) => field.onChange(option.value)}
                              options={availableTourneysOptions}
                              fullWidth
                              asModal />
                            <ErrorMessage>{errors.tourney_id?.message}</ErrorMessage>
                          </FormControl>
                        );
                      }}
                    />

                    {selectedTournament && (
                      <Text $m={0} $color="fg-soft-color" $size="sm" $fontWeight={500}>
                        <span>{`${t('global.time')}: ${formatDate(selectedTournament.date* 1000, 'dd.MM.yyyy HH:mm')}`}</span>
                        <Text as="span" $mx={3} />
                        <span>{`${t('global.buyin')}: ${(Number(selectedTournament.rake) + Number(selectedTournament.stake)).toFixed(2)}`}</span>
                      </Text>
                    )}
                  </HStack>

                  {/* open manual input */}
                  <Button onClick={() => setHasTourneyManualInput(true)}>
                    {t('tournaments.manualInputTourney')}
                  </Button>
                </>
              ) : (
                <>
                  <HStack $alignItems="start">
                    {/* tourney name */}
                    <FormControl invalid={!!errors.tourney_name} $flex={1}>
                      <Label>{t('tournaments.tourneyName')}</Label>
                      <TextInput
                        {...register('tourney_name', {
                          required: t('global.requiredField'),
                        })}
                        placeholder={t('tournaments.tourneyName')}
                        disabled={!arePrimaryValuesEntered}
                      />
                      <ErrorMessage>{errors.tourney_name?.message}</ErrorMessage>
                    </FormControl>

                    {/* tourney time */}
                    <FormControl invalid={!!errors.tourney_time} $w={32}>
                      <Label>{t('global.time')}</Label>
                      <TextInput
                        {...registerWithMask('tourney_time', 'time', {
                          required: t('global.requiredField'),
                          pattern: {
                            value: /^([01]\d|2[0-3]):([0-5]\d)$/,
                            message: t('tournaments.invalidTimeFormat'),
                          },
                        })}
                        placeholder={t('global.time')}
                        disabled={!arePrimaryValuesEntered}
                      />
                      <ErrorMessage>{errors.tourney_time?.message}</ErrorMessage>
                    </FormControl>
                  </HStack>

                  {/* show list of available tourneys  */}
                  {!!availableTourneys?.length && (
                    <Button onClick={() => setHasTourneyManualInput(false)}>
                      {t('tournaments.showTournamentsList')}
                    </Button>
                  )}
                </>
              )}

              {/* comment */}
              <FormControl invalid={!!errors.comment}>
                <Label>{t('global.comment')}</Label>
                <TextArea {...register('comment')} placeholder={t('global.comment')} />
                <ErrorMessage>{errors.comment?.message}</ErrorMessage>
              </FormControl>
            </VStack>

            {/* footer */}
            <HStack $justifyContent="end" $mt={8}>
              <Button $colorScheme="neutral" onClick={() => setOpen(false)}>
                {t('global.cancel')}
              </Button>
              <Button type="submit" loading={isSubmitting}>
                {t('global.request')}
              </Button>
            </HStack>
          </form>
        }
      />
    </>
  );
};
