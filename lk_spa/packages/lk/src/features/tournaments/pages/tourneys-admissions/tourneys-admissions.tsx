import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ColDef } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { format } from 'date-fns';
import { AdmissionParam, useGetTourneysAdmissions } from '@/features/tournaments/api';
import { AlertCard } from '@/shared/components/alert-card';
import { Box } from '@/shared/components/box';
import { Heading } from '@/shared/components/heading';
import { Skeleton } from '@/shared/components/skeleton';
import { useTableTheme } from '@/theme/ag-grid';
import { TournamentsHeader } from '../../components/tournaments-header';
import { RequestAdmission } from './request-admission';
import { TourneyAdmissionControls } from './tourney-admission-controls';
import { TourneyResultsModalProvider } from './tourney-results-modal-provider';

export const TourneysAdmissions = () => {
  const { t } = useTranslation();

  const { data: items, isLoading, isError, isFetching } = useGetTourneysAdmissions();

  const tableTheme = useTableTheme();

  const columnDefs = useMemo<ColDef<AdmissionParam>[]>(
    () => [
      {
        field: 'room.abrname',
        headerName: t('global.room'),
        flex: 1,
        sortable: false,
        resizable: false,
      },
      {
        field: 'name',
        headerName: t('global.name'),
        flex: 4,
        sortable: false,
        resizable: false,
      },
      {
        field: 'date',
        headerName: t('global.date'),
        flex: 2,
        sortable: false,
        resizable: false,
        valueGetter: ({ data }) => format(new Date(data!.date * 1000), 'dd.MM.yyyy HH:mm'),
      },
      {
        field: 'bi',
        headerName: t('global.buyin'),
        flex: 2,
        sortable: false,
        resizable: false,
      },
      {
        field: 'answer',
        headerName: t('global.answer'),
        flex: 4,
        sortable: false,
        resizable: false,
        cellRenderer: ({ data }: { data: AdmissionParam }) => {
          return <TourneyAdmissionControls data={data} />;
        },
      },
    ],
    [t],
  );

  return (
    <TourneyResultsModalProvider>
      <TournamentsHeader />

      <Heading $fontWeight={500} $mt={2} $mb={4}>
        {t('tournaments.tournamentAdmissions')}
      </Heading>

      {isError ? (
        <AlertCard />
      ) : isLoading ? (
        <Skeleton $flex={1} />
      ) : (
        <>
          <Box $h="calc(100vh - 16rem)">
            <AgGridReact
              rowData={items}
              columnDefs={columnDefs}
              /** behaviour */
              suppressRowHoverHighlight
              suppressMultiSort
              suppressCellFocus
              suppressMovableColumns
              /** appearance */
              loading={isFetching}
              theme={tableTheme}
            />
          </Box>
        </>
      )}

      <RequestAdmission />
    </TourneyResultsModalProvider>
  );
};
