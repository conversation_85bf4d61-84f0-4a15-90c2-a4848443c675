import { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { AdmissionParam, useGetSelfPlayedData, useSaveTourneyResults } from '@/features/tournaments/api';
import { HStack, VStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { Dialog } from '@/shared/components/dialog';
import { ErrorMessage, FormControl, Label } from '@/shared/components/form-control';
import { TextInput } from '@/shared/components/input';
import { Select } from '@/shared/components/select';
import { Spinner } from '@/shared/components/spinner';
import { Text } from '@/shared/components/text';
import { useToast } from '@/shared/components/toast';
import { useHookFormMask } from '@/shared/hooks/use-hook-form-mask';

const defaultValues = {
  bi: 0,
  reentry: 0,
  ko: 0,
  prize: 0,
  currency_id: 0,
};

interface Props {
  admission: AdmissionParam | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const TourneyResultsModal = (props: Props) => {
  const { admission, open, onOpenChange } = props;

  const { t } = useTranslation();
  const toast = useToast();

  const { data: playedData, isLoading: isPlayedDataLoading } = useGetSelfPlayedData({
    id: admission?.id,
    enabled: !!admission,
  });

  const currencySelectOptions = useMemo(
    () =>
      playedData?.available_currencies.map((currency) => ({
        label: currency.abrname,
        value: currency.id,
        field: '',
      })) || [],
    [playedData?.available_currencies],
  );

  const {
    register,
    formState: { isSubmitting, errors },
    handleSubmit,
    control,
    reset,
    watch,
    setValue,
  } = useForm({ mode: 'onChange', defaultValues });
  const registerWithMask = useHookFormMask(register);

  // set default currency
  useEffect(() => {
    setValue('currency_id', playedData?.default_values.currency_id || 0);
  }, [playedData?.default_values.currency_id, setValue]);

  // reset form on open
  useEffect(() => {
    if (open) reset();
  }, [open, reset]);

  const [profit, setProfit] = useState('0.00');

  // calculate profit
  useEffect(() => {
    const subscription = watch((values) => {
      const { bi, reentry, ko, prize } = values as {
        bi: number;
        reentry: number;
        ko: number;
        prize: number;
      };

      if ([bi, reentry, ko, prize].some((value) => isNaN(value))) {
        setProfit('0.00');
      } else {
        const calculatedProfit = ko + prize - bi * (1 + reentry);
        setProfit(calculatedProfit.toFixed(2));
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  const { mutateAsync: saveResults } = useSaveTourneyResults();

  const submit = handleSubmit(async (values) => {
    try {
      await saveResults({ id: admission!.id, data: values });
      toast.success({ title: t('global.done') });
      onOpenChange(false);
    } catch (error) {
      toast.fetchError(error);
    }
  });

  const numFieldParams = {
    required: t('global.requiredField'),
    valueAsNumber: true,
  };

  return (
    <Dialog
      title={t('tournaments.enterResults')}
      open={open}
      onOpenChange={onOpenChange}
      content={
        isPlayedDataLoading ? (
          <HStack $pt={32} $pb={40} children={<Spinner $size={32} />} />
        ) : (
          <form onSubmit={submit}>
            <VStack $alignItems="stretch" $gap={5}>
              {/* header */}
              {/* TOOD: fix issue with flickering of admission name */}
              <Text $m={0}>
                <span>{`${t('global.tourney')}: `}</span>
                <Text as="span" $color="accent">
                  {admission?.name}
                </Text>
                <span>{` - ${t('tournaments.fromSelf').toLowerCase()}`}</span>
              </Text>

              <Controller
                control={control}
                name="currency_id"
                rules={{ required: t('global.requiredField') }}
                render={({ field, fieldState: { error } }) => (
                  <FormControl invalid={!!error}>
                    <Label>{t('global.currency')}</Label>
                    <Select
                      {...field}
                      options={currencySelectOptions}
                      placeholder={t('global.currency')}
                      fullWidth
                    />
                    <ErrorMessage>{error?.message}</ErrorMessage>
                  </FormControl>
                )}
              />

              <HStack $alignItems="start">
                {/* buyin */}
                <FormControl invalid={!!errors.bi}>
                  <Label>{t('global.buyin')}</Label>
                  <TextInput
                    {...registerWithMask('bi', 'numeric', numFieldParams)}
                    placeholder={t('global.buyin')}
                  />
                  <ErrorMessage>{errors.bi?.message}</ErrorMessage>
                </FormControl>

                {/* reentry */}
                <FormControl invalid={!!errors.reentry}>
                  <Label>{t('global.reentry')}</Label>
                  <TextInput
                    {...registerWithMask('reentry', 'numeric', numFieldParams)}
                    placeholder={t('global.reentry')}
                  />
                  <ErrorMessage>{errors.reentry?.message}</ErrorMessage>
                </FormControl>

                {/* ko */}
                <FormControl invalid={!!errors.ko}>
                  <Label>{t('global.ko')}</Label>
                  <TextInput {...registerWithMask('ko', 'numeric', numFieldParams)} placeholder={t('global.ko')} />
                  <ErrorMessage>{errors.ko?.message}</ErrorMessage>
                </FormControl>

                {/* prize */}
                <FormControl invalid={!!errors.prize}>
                  <Label>Prize</Label>
                  <TextInput {...registerWithMask('prize', 'numeric', numFieldParams)} placeholder="Prize" />
                  <ErrorMessage>{errors.prize?.message}</ErrorMessage>
                </FormControl>
              </HStack>

              <Text $m={0}>{`Profit: ${profit}`}</Text>

              <Text $m={0} $color="fg-soft-color">
                {t('tournaments.transactionWillBeRecorded')}
              </Text>
            </VStack>

            <HStack $justifyContent="end" $mt={8}>
              <Button $colorScheme="neutral" onClick={() => onOpenChange(false)}>
                {t('global.cancel')}
              </Button>
              <Button type="submit" loading={isSubmitting}>
                {t('global.enterResults')}
              </Button>
            </HStack>
          </form>
        )
      }
    />
  );
};
