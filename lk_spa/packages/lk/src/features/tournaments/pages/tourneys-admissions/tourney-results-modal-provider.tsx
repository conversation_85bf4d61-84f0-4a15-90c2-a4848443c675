import { createContext, useContext, useState } from 'react';
import { AdmissionParam } from '@/features/tournaments/api';
import { TourneyResultsModal } from './tourney-results-modal';

export interface TourneyResultsModalContextValue {
  open: (admission: AdmissionParam) => void;
  close: () => void;
}

const TourneyResultsModalContext = createContext<TourneyResultsModalContextValue>({
  open: () => {},
  close: () => {},
});

export const useTourneyResultsModal = () => useContext(TourneyResultsModalContext);

export const TourneyResultsModalProvider = ({ children }: React.PropsWithChildren) => {
  const [admission, setAdmission] = useState<AdmissionParam | null>(null);

  const open = (admission: AdmissionParam) => {
    setAdmission(admission);
  };

  const close = () => {
    setAdmission(null);
  };

  const value: TourneyResultsModalContextValue = {
    open,
    close: () => setAdmission(null),
  };

  return (
    <TourneyResultsModalContext.Provider value={value}>
      {children}
      <TourneyResultsModal admission={admission} open={!!admission} onOpenChange={close} />
    </TourneyResultsModalContext.Provider>
  );
};
