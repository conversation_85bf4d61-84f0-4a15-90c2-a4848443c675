import { useTranslation } from 'react-i18next';
import { AdmissionParam, useDidntPlayTourney, useRemoveTourneyAdmission } from '@/features/tournaments/api';
import { HStack } from '@/shared/components/box';
import { Button } from '@/shared/components/button';
import { Tag } from '@/shared/components/tag';
import { Text } from '@/shared/components/text';
import { useToast } from '@/shared/components/toast';
import { useTourneyResultsModal } from './tourney-results-modal-provider';

type Props = {
  data: AdmissionParam;
};

export const TourneyAdmissionControls = (props: Props) => {
  const { data } = props;

  const { t } = useTranslation();
  const toast = useToast();

  const getStatusLabel = (statusCode: number) => {
    switch (statusCode) {
      case 0:
        return t('global.processing');
      case 1:
        return t('tournaments.fromSelf');
      case 2:
        return t('tournaments.intoThePackage');
      case 3:
        return t('tournaments.partOfThePackage');
      case 4:
        return t('tournaments.alwaysInPackage');
      case 5:
        return t('global.notAllowed');
      case 10:
        return t('global.rejected');
      default:
        return t('global.unknownStatus');
    }
  };

  const colorShceme =
    data.answer === 0 ? 'neutral' : data.answer === 1 ? 'red' : data.answer === 2 ? 'green' : 'orange';

  const { mutateAsync: removeTourneyAdmission, isPending: isRemovePending } = useRemoveTourneyAdmission();
  const handleRemoveTourneyAdmission = async () => {
    try {
      await removeTourneyAdmission(data.id);
      toast.success({ title: t('global.deleted') });
    } catch (error) {
      toast.fetchError(error);
    }
  };

  const { mutateAsync: didntPlayTourney, isPending: isDidntPlayPending } = useDidntPlayTourney();
  const handleDidntPlayTourney = async () => {
    try {
      await didntPlayTourney(data.id);
      toast.success({ title: t('global.done') });
    } catch (error) {
      toast.fetchError(error);
    }
  };

  const tourneyResultsModal = useTourneyResultsModal();

  console.log(data);

  return (
    <HStack $justifyContent="space-between" $h="100%">
      <Tag $colorScheme={colorShceme}>{getStatusLabel(data.answer)}</Tag>

      <HStack>
        {data.write_flag ? (
          <Text $m={0}>{t('global.written')}</Text>
        ) : data.answer === 1 || data.answer === 3 ? (
          <>
            <Button //
              $size="sm"
              onClick={() => tourneyResultsModal.open(data)}
              children={t('global.played')}
            />
            <Button
              $size="sm"
              $variant="outline"
              $colorScheme="red"
              onClick={handleDidntPlayTourney}
              loading={isDidntPlayPending}
              children={t('global.didntPlay')}
            />
          </>
        ) : data.answer === 0 ? (
          <Button
            $size="sm"
            $variant="outline"
            $colorScheme="red"
            onClick={handleRemoveTourneyAdmission}
            loading={isRemovePending}
            children={t('global.remove')}
          />
        ) : null}
      </HStack>
    </HStack>
  );
};
