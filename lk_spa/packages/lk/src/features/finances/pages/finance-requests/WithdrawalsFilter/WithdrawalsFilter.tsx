import {FinanceBlock} from '@/features/finances/components/FinanceBlock/FinanceBlock'
import {memo, useEffect} from 'react';
import {Controller, useForm} from 'react-hook-form';
import styles from '@/__old/components/Finances/finances.module.scss';
import {AccountSelect} from '@/__old/components/Finances/AccountSelect/AccountSelect';
import type {FinanceRoom, WithdrawalRequestsFilter} from '@/__old/types/finances';
import type {FilterComponent} from '@/__old/types/table';
import {Checkbox, Row} from '@funfarm/kit';

export const WithdrawalsFilter: FilterComponent<WithdrawalRequestsFilter> =
    memo(({values, setValues}) => {
    const { control, watch } = useForm({
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    const currentValues = watch();

    useEffect(() => {
        if (JSON.stringify(currentValues.rooms) !== JSON.stringify(values?.rooms)) {
            setValues({...values, rooms: currentValues.rooms});
        }

    }, [currentValues, setValues, values]);

    return (
        <FinanceBlock className={styles.transfersFilter}>
            <Row gap={2}>
                <AccountSelect
                    control={control}
                    label="Счёт"
                    labelPosition="inside"
                    filterFunction={(room: FinanceRoom) =>
                        Number(room.balance) > 0
                    }
                    required={false}
                    multiChoice
                    action="withdraw"
                />

                <Controller
                    control={control}
                    name="countries"
                    render={({ field }) => (
                        <Checkbox
                            {...field}
                            label="Россия и Беларусь"
                            labelPosition="right"
                            type="radio"
                            name="countries"
                            value="ru"
                            checked={values?.region_type === 'ru'}
                            onChange={(e) =>
                                setValues({
                                    ...values,
                                    region_type: e.target.checked ? 'ru' : undefined,
                                })
                            }
                        />
                    )}
                />

                <Controller
                    control={control}
                    name="countries"
                    render={({ field }) => (
                        <Checkbox
                            {...field}
                            label="Остальные страны"
                            labelPosition="right"
                            type="radio"
                            name="countries"
                            value="other"
                            checked={values?.region_type === 'other'}
                            onChange={(e) =>
                                setValues({
                                    ...values,
                                    region_type: e.target.checked ? 'other' : undefined,
                                })
                            }
                        />
                    )}
                />
            </Row>
        </FinanceBlock>
    );
    });
