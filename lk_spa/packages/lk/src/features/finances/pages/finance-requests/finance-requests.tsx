import {useContext, useState} from 'react';
import {
    TransactionsHistory
} from '@/__old/components/Finances/TransactionsHistory/TransactionsHistory';
import {NavigationContext} from '@/__old/components/Layout/NavigationProvider';
import type {WithdrawalRequest} from '@/__old/types/finances';
import {CreateRequest} from "@/__old/components/Finances/Withdrawals/CreateRequest";
import {SendMoney} from "@/__old/components/Finances/Withdrawals/SendMoney";
import {PageHeading} from '@/shared/kit-themed-components';
import {Box, Button, Dialog, HStack} from '@/shared/components';
import {
    FinanceRequestTable
} from "@/features/finances/components/finance-request-table/finance-request-table";


export const FinanceRequests = () => {
    const [newRequestOpened, setNewRequestOpened] = useState(false);
    const [sendMoneyRequest, setSendMoneyRequest] = useState<WithdrawalRequest>();
    const {openDrawer} = useContext(NavigationContext);

    return (
        <>
            <PageHeading
                tabs={[
                    {title: 'Баланс', to: '/finance-balance'},
                    {title: 'Переводы', to: '/finance-requests'},
                ]}
                title="Финансы"
            />
            <PageHeading
                title="Активные запросы"
                tag="h2"
                renderButtons={() => (
                    <HStack $gap={4}>
                        <Button
                            onClick={() => openDrawer!(<TransactionsHistory/>)}
                            $variant={"filled"}
                        >
                            История переводов
                        </Button>
                        <Button
                            onClick={() => setNewRequestOpened(true)}
                        >
                            Создать новый запрос
                        </Button>
                        {newRequestOpened && (
                            <Dialog
                                title="Создать запрос"
                                open={newRequestOpened}
                                onOpenChange={() => setNewRequestOpened(false)}
                                content={<CreateRequest
                                    onClose={() => setNewRequestOpened(false)}/>}
                            >

                            </Dialog>
                        )}
                    </HStack>
                )}
            />
            <Box >
                <FinanceRequestTable setSendMoneyRequest={setSendMoneyRequest}/>
            </Box>
            {sendMoneyRequest && (
                <Dialog
                    title="Отправка денег"
                    open={!!sendMoneyRequest}
                    onOpenChange={() => setSendMoneyRequest(undefined)}
                    content={    <SendMoney
                        request={sendMoneyRequest}
                        onClose={() => setSendMoneyRequest(undefined)}
                    />}
                >

                </Dialog>
            )}
        </>
    );
};
