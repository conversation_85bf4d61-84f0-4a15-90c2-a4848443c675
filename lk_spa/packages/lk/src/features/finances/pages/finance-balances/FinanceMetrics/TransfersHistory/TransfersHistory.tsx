import {
  TransfersFilter
} from '@/features/finances/pages/finance-balances/FinanceMetrics/TransfersHistory/TransfersFilter/TransfersFilter'
import {useContext, useMemo, useState} from 'react';
import type {ColDef} from 'ag-grid-community';
import {AllCommunityModule, ModuleRegistry} from 'ag-grid-community';
import {AgGridReact} from 'ag-grid-react';
import {useQuery} from '@tanstack/react-query';
import {getWithdrawals} from '@/__old/actions/finances';
import {DrawerLayout} from '@/__old/components/Finances/DrawerLayout';
import {NavigationContext} from '@/__old/components/Layout/NavigationProvider';
import {useBalancesAndBrm} from '@/__old/hooks/useBalancesAndBrm';
import {usePagination} from '@/__old/hooks/usePagination';
import type {Withdrawal, WithdrawalsFilter} from '@/__old/types/finances';
import {But<PERSON>, Dialog, HStack} from '@/shared/components';
import {AddOperation} from "@/__old/components/Finances/TransfersHistory/AddOperation";
import Pagination from '@funfarm/kit/Pagination/Pagination';
import columnTypes from '@funfarm/kit/DataGrid/dataGrid.columnTypes';
import {useTableTheme} from '@/theme/ag-grid';
import styles from "./TransfersHistory.module.scss"

export const TransfersHistory = () => {
    const [operationOpened, setOperationOpened] = useState(false);
    const { closeDrawer } = useContext(NavigationContext);
    const { metrics } = useBalancesAndBrm();
    const { page, setPage, pageSize } = usePagination();
    const [filter, setFilter] = useState<WithdrawalsFilter>();
    const theme = useTableTheme();

    const { data, isLoading } = useQuery({
        queryKey: ['finances/withdraws', filter, page, pageSize],
        queryFn: () => getWithdrawals(filter, (page - 1) * pageSize, pageSize + 1),
    });

    const trimmedData = useMemo(() => data?.slice(0, pageSize), [data, pageSize]);

    const columnDefs = useMemo<ColDef<Withdrawal>[]>(
        () => [
            { field: 'date', headerName: 'Дата', type: 'date' },
            { field: 'curramount', headerName: 'Сумма', type: ['highlightAmount'] },
            { field: 'room', headerName: 'Счёт', type: 'accountLogo' },
            { field: 'type', headerName: 'Тип' },
            { field: 'balance', headerName: 'Остаток', type: 'usdValue' },
            {
                field: 'modcom',
                headerName: 'Статус',
                type: 'badge',
                refData: {
                    Проведено: 'green',
                    Удален: 'red',
                },
            },
        ],
        [],
    );

    const autoSizeStrategy = useMemo(() => ({
        type: 'fitGridWidth' as const,
    }), []);

    return (
        <DrawerLayout
            pageHeadingProps={{
                title: 'История операций',
                renderButtons: () => (
                    <HStack $gap={4}>
                        <Button
                            $variant={"filled"}
                            $icon={"plus"}
                            onClick={() => setOperationOpened(true)}
                        >
                            Добавить операцию
                        </Button>
                        <Button
                            $variant={"solid"}
                            $icon={"close"}
                            onClick={closeDrawer}
                            className={styles.close}
                        >
                        </Button>
                        {operationOpened && (
                            <Dialog
                                title="Добавить операцию"
                                open={operationOpened}
                                onOpenChange={() => setOperationOpened(false)}
                                content={   <AddOperation onClose={() => setOperationOpened(false)} />}
                            >

                            </Dialog>
                        )}
                    </HStack>
                ),
            }}
            metricsProps={{
                caption: 'Доступно к выводу',
                value: metrics?.dvijpl || '-',
            }}
        >
            <TransfersFilter values={filter} setValues={setFilter} />
            <AgGridReact
                loading={isLoading}
                rowData={trimmedData}
                columnDefs={columnDefs}
                columnTypes={columnTypes}
                suppressDragLeaveHidesColumns
                autoSizeStrategy={autoSizeStrategy}
                theme={theme}
                suppressRowHoverHighlight
                domLayout="autoHeight"
            />
            <Pagination
                page={page}
                setPage={setPage}
                loading={isLoading}
                hasNextPage={!isLoading && data?.length ? data?.length > pageSize : false}
            />
        </DrawerLayout>
    );
};

// Register all Community features
ModuleRegistry.registerModules([AllCommunityModule]);
