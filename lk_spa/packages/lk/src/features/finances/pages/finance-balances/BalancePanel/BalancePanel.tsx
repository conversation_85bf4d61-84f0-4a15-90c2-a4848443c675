import {useCallback, useState} from 'react';
import {deleteBalance} from '@/__old/actions/finances';
import {CreateRequest} from '@/__old/components/Finances/Withdrawals/CreateRequest';
import {useBalancesAndBrm} from '@/__old/hooks/useBalancesAndBrm';
import type {BalanceItem} from '@/__old/types/finances';
import {Error} from '@funfarm/kit';
import styles from './BalancePanel.module.scss';
import {FinanceBlock} from "@/features/finances/components/FinanceBlock/FinanceBlock";
import TableSkeleton from "@/__old/components/Finances/TableSkeleton";
import {Button, Dialog, VStack,} from '@/shared/components';
import {BalanceHistory} from './BalanceHistory/BalanceHistory';
import {BalanceLine} from "./BalanceLine/BalanceLine";
import {BalanceToAddToWay} from "./BalanceToAddToWay/BalanceToAddToWay";

export const BalancePanel = () => {
    const [historyOpen, setHistoryOpen] = useState(false);
    const [balanceToChange, setBalanceToChange] = useState<BalanceItem>();

    const { balances, inTheWay, toAddToWay, isLoading, isBalancesError, refetch } = useBalancesAndBrm();

    const onBalanceDelete = useCallback(
        (item: BalanceItem) => {
            deleteBalance(item.room_id).then(() => {
                refetch();
            });
        },
        [refetch],
    );

    return (
        <div className={styles.balancePanel}>
            <FinanceBlock
                header="Баланс"
                helper={
                    <Button $variant={"plain"} onClick={() => setHistoryOpen(true)} style={{fontSize:14}} >
                        <span className={styles.panelHeaderButton}>Посмотреть историю</span>
                    </Button>
                }
                className={styles.balanceList}
            >
                {isLoading ? (
                    <TableSkeleton columns={3} />
                ) : isBalancesError || !balances.length ? (
                    <Error message="Ошибка получения данных" />
                ) : (
                    <VStack $gap={3} $alignItems={"flex-start"} >
                        {!isLoading &&
                            balances.map((details: BalanceItem, index) => (
                                <BalanceLine
                                    item={details}
                                    openChangeBalance={(item: BalanceItem) => setBalanceToChange(item)}
                                    key={index}
                                />
                            ))}
                        {(!!inTheWay.length || !!toAddToWay?.length) && (
                            <>
                                <hr />
                                <h3>В пути</h3>
                                {inTheWay.map((details, index) => (
                                    <BalanceLine
                                        item={details}
                                        openChangeBalance={(item: BalanceItem) => setBalanceToChange(item)}
                                        deleteBalance={onBalanceDelete}
                                        key={index}
                                    />
                                ))}
                                {toAddToWay?.map((details, index) => <BalanceToAddToWay item={details} key={index} />)}
                            </>
                        )}
                        {balanceToChange && (
                            <Dialog
                                title="Создать запрос"
                                open={!!balanceToChange}
                                onOpenChange={() => setBalanceToChange(undefined)}
                                content={<CreateRequest balance={balanceToChange} onClose={() => setBalanceToChange(undefined)} />}
                            >

                            </Dialog>
                        )}
                    </VStack>
                )}
            </FinanceBlock>
            {/*
      {historyOpen && (
        <Dialog
          header="История заполнения балансов"
          open={historyOpen}
          onClose={() => setHistoryOpen(false)}
          xlarge
        >
          <BalanceHistoryOld />
        </Dialog>
      )} */}
            <BalanceHistory open={historyOpen} onOpenChange={setHistoryOpen} />
        </div>
    );
};
