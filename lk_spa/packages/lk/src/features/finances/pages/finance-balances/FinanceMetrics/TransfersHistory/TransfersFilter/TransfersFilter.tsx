import styles from '@/__old/components/Finances/finances.module.scss';
import type {WithdrawalsFilter} from '@/__old/types/finances';
import type {FilterComponent} from '@/__old/types/table';
import {InputDateRange} from '@funfarm/kit/InputDate/InputDateRange';
import {FinanceBlock} from "@/features/finances/components/FinanceBlock/FinanceBlock";
import {Checkbox, HStack, Text} from "@/shared/components";

export const TransfersFilter: FilterComponent<WithdrawalsFilter> = ({
                                                                        values,
                                                                        setValues,
                                                                    }) => {
    /*const {control, watch} = useForm({
      mode: 'onChange',
      reValidateMode: 'onChange',
    });

    const currentValues = watch();*/

    return (
        <FinanceBlock className={styles.transfersFilter}>
            <HStack $gap={2}  $justifyContent={"flex-start"}>
                <div>
                    <InputDateRange
                        placeholder="Период"
                        className={styles.dateRangeInput}
                        value={[values?.date_start, values?.date_end]}
                        // startDate={undefined}
                        // endDate={undefined}
                        label="Период"
                        dateFormat="dd.MM.yyyy"
                        labelPosition="inside"
                        onChange={([startDate, endDate]) =>
                            setValues({
                                ...values,
                                startDate,
                                endDate,
                            })
                        }
                    />
                </div>
                <HStack>
                    <Checkbox
                        name="Пополнение"
                        checked={values?.operationType === 'in'}
                        onClick={() =>
                            setValues({
                                ...values,
                                operationType: 'in',
                            })
                        }
                    />
                    <Text $size={14}>Пополнение</Text>
                </HStack>


                <HStack> <Checkbox
                    name="Списание"
                    checked={values?.operationType === 'out'}
                    onClick={() =>
                        setValues({
                            ...values,
                            operationType: 'out',
                        })
                    }
                />
                    <Text $size={14}>Списание</Text>
                </HStack>

            </HStack>
        </FinanceBlock>
    );
};
