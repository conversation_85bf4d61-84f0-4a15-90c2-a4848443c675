import {Metrics, type MetricsProps} from '@/features/finances/components/Metrics/Metrics'
import { type ReactElement, useContext } from 'react';
import styles from '@/__old/components/Finances/finances.module.scss';
import { NavigationContext } from '@/__old/components/Layout/NavigationProvider';
import { PageHeading } from '@/__old/components/PageHeading/PageHeading';
import {Button, type IconName} from "@/shared/components";

type Props = {
    title: string;
    button: string;
    buttonIcon: IconName;
    drawerContent: ReactElement;
    items: MetricsProps[];
    isLoading?: boolean;
};

export const MetricsSection = ({title, button, buttonIcon, drawerContent, items, isLoading = false}: Props) => {
    const { openDrawer } = useContext(NavigationContext);

    return (
        <>
            <PageHeading
                title={title}
                renderButtons={() => (
                    <Button $variant={"filled"} $icon={buttonIcon} onClick={() => openDrawer!(drawerContent)} >
                        {button}
                    </Button>
                )}
                tag="h2"
            />
            <div className={styles.metricsContainer}>
                {items.map((item, i) => (
                    <Metrics {...item} isLoading={isLoading} key={i} />
                ))}
            </div>
        </>
    );
};
