import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AgGridReact } from 'ag-grid-react';
import { useGetBalanceHistory } from '@/features/finances/api';
import { AlertCard } from '@/shared/components/alert-card';
import { Box } from '@/shared/components/box';
import { Dialog } from '@/shared/components/dialog';
import { useTableTheme } from '@/theme/ag-grid';
import Pagination from '@funfarm/kit/Pagination/Pagination';
import { useBalanceHistoryColumns } from './BalanceHistory.resources';
import { CurrentPackageInfo } from './CurrentPackageInfo/CurrentPackageInfo';

interface Props {
  open: boolean;
  onOpenChange?: (open: boolean) => void;
}

const initialPagination = { take: 20, skip: 0 };

export const BalanceHistory = ({ open, onOpenChange }: Props) => {
  const { t } = useTranslation();

  const [pagination, setPagination] = useState(initialPagination);

  useEffect(() => {
    if (open) setPagination(initialPagination);
  }, [open]);

  const {
    data: balanceHistoryData,
    isLoading: isBalanceHistoryLoading,
    isFetching: isBalanceHistoryFetching,
    isError: isBalanceHistoryError,
  } = useGetBalanceHistory({ enabled: open, pagination });

  const {
    columnDefs,
    isLoading: isRoomsLoading,
    isError: isRoomsError,
  } = useBalanceHistoryColumns({ enabled: open });

  const tableTheme = useTableTheme();

  const isLoading = isRoomsLoading || isBalanceHistoryLoading || isBalanceHistoryFetching;
  const isError = isRoomsError || isBalanceHistoryError;

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      title={t('finances.balanceHistory')}
      width="max"
      content={
        <>
          <CurrentPackageInfo />

          {isError ? (
            <AlertCard />
          ) : (
            <Box $mt={4} $h="calc(100vh - 18rem)">
              <AgGridReact
                rowData={balanceHistoryData?.balances}
                columnDefs={columnDefs}
                /** behaviour */
                suppressRowHoverHighlight
                suppressMultiSort
                suppressCellFocus
                /** appearance */
                loading={isLoading}
                theme={tableTheme}
              />
            </Box>
          )}

          {/* TODO: create new component */}
          <Pagination
            page={pagination.skip / pagination.take + 1}
            setPage={(page) => setPagination({ take: pagination.take, skip: (page - 1) * pagination.take })}
          />
        </>
      }
    />
  );
};
