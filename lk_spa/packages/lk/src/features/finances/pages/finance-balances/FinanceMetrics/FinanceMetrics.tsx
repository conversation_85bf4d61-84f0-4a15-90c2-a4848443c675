import {useMemo} from 'react';
import {PlayersPackages} from '@/__old/components/Finances/PlayerPackages/PlayersPackages';
import {TransactionsHistory} from '@/__old/components/Finances/TransactionsHistory/TransactionsHistory';
import {useBalancesAndBrm} from '@/__old/hooks/useBalancesAndBrm';
import {Error} from '@funfarm/kit';
import styles from './FinanceMetrics.module.scss';
import {MetricsSection} from "./MetricsSection/MetricsSection";
import {TransfersHistory} from "./TransfersHistory/TransfersHistory";

export const FinanceMetrics = () => {
    const { metrics, currentPackage, isLoading, isMetricsError, isCurrentPackageError } = useBalancesAndBrm();

    const currentPackageProfit = useMemo(
        () => Number(currentPackage?.profit.profit),
        [currentPackage?.profit.profit],
    );

    const playerProfit = useMemo(
        () =>
            currentPackageProfit > 0
                ? (currentPackageProfit * Number(metrics?.percprofit)) / 100
                : (currentPackageProfit * Number(metrics?.percbuyin)) / 100,
        [currentPackageProfit, metrics],
    );

    return isMetricsError || isCurrentPackageError ? (
        <Error message="Ошибка получения данных" />
    ) : (
        <div className={styles.financesContent}>
            <MetricsSection
                title="Текущий пакет"
                button="Пакеты игрока"
                buttonIcon={"plus"}
                drawerContent={<PlayersPackages packageProfit={currentPackageProfit} />}
                isLoading={isLoading}
                items={[
                    { caption: 'Дата начала пакета', value: metrics?.datepack },
                    {
                        caption: 'Процент от бай-ина',
                        value: metrics?.percbuyin,
                        percentage: true,
                    },
                    {
                        caption: 'Процент от профита',
                        value: metrics?.percprofit,
                        percentage: true,
                    },
                    { caption: 'Сыграно МТТ в пакете', value: metrics?.mttpack },
                    {
                        caption: 'Профит пакета',
                        value: currentPackageProfit,
                        currency: 'USD',
                    },
                    {
                        caption: 'Профит игрока',
                        value:
                            currentPackageProfit > 0
                                ? (currentPackageProfit * Number(metrics?.percprofit)) / 100
                                : (currentPackageProfit * Number(metrics?.percbuyin)) / 100,
                        currency: 'USD',
                    },
                    {
                        caption: 'Профит проекта',
                        value: currentPackageProfit - playerProfit,
                        currency: 'USD',
                    },
                ]}
            />

            <MetricsSection
                title="Деньги игрока"
                button="История операций"
                buttonIcon={"plus"}
                drawerContent={<TransfersHistory />}
                isLoading={isLoading}
                items={[
                    {
                        caption: 'Нужно от игрока',
                        value: metrics?.needuser,
                        currency: 'USD',
                    },
                    { caption: 'После расчёта', value: metrics?.wmoney, currency: 'USD' },
                    {
                        caption: 'Доступно к выводу',
                        value: metrics?.dvijpl,
                        currency: 'USD',
                    },
                ]}
            />

            <MetricsSection
                title="Деньги проекта"
                button="История переводов"
                buttonIcon={"plus"}
                drawerContent={<TransactionsHistory />}
                isLoading={isLoading}
                items={[
                    {
                        caption: 'Нужно от проекта',
                        value: metrics?.need,
                        currency: 'USD',
                    },
                    {
                        caption: 'Средняя загрузка',
                        value: metrics?.averload,
                        currency: 'USD',
                    },
                    {
                        caption: 'Денег проекта у игрока',
                        value: metrics?.moneyproject,
                        currency: 'USD',
                    },
                    {
                        caption: 'Излишки',
                        value: metrics?.overage,
                        currency: 'USD',
                        ...(metrics?.overage &&
                            metrics.overage > 0 && {
                                className: styles.redMetrics,
                                // button: (
                                //     <Button
                                //         view="outlined"
                                //         size="medium"
                                //         color="red"
                                //         label="К переводам"
                                //         onClick={() => navigate("/finance-requests")}
                                //     />
                                // ),
                            }),
                    },
                    {
                        caption: 'КЭБ',
                        value: metrics?.keb_30d ?? undefined,
                    },
                ]}
            />
        </div>
    );
};
