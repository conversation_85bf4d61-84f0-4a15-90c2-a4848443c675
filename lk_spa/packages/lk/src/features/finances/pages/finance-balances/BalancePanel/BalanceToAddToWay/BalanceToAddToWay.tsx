import type {IError} from '@funfarm/kit'
import {useState} from 'react';
import {addBalance} from '@/__old/actions/finances';
import {useBalancesAndBrm} from '@/__old/hooks/useBalancesAndBrm';
import type {BalanceToAddToWayLine} from '@/__old/types/finances';
import {Box, Button, HStack, useToast} from "@/shared/components";

type Props = {
    item: BalanceToAddToWayLine;
};

export const BalanceToAddToWay = ({ item }: Props) => {
    const [loading, setLoading] = useState(false);
    const toast = useToast()

    const { refetch } = useBalancesAndBrm();

    return (
        <HStack $justifyContent="space-between" $w="100%">
            <Box>
                <h4>{item.title}</h4>
            </Box>
            <HStack $justifyContent="space-between" $gap={1}>
                <Button
                    $variant="filled"
                    onClick={() => {
                        setLoading(true);
                        addBalance(item.id)
                            .then(() => {
                                toast.success({ title: 'Баланс добавлен' });
                                refetch();
                            })
                            .catch((error: IError) => {
                                console.error('Error while adding a balance to a way: ', error);
                            })
                            .finally(() => setLoading(false));
                    }}
                    $icon="plus"
                    disabled={loading}
                    style={{width:36}}
                />
            </HStack>
        </HStack>
    );
};
