import styles from './finance-balances.module.scss';
import {PageHeading} from '@/shared/kit-themed-components';
import {VStack} from '@/shared/components';
import {BalancePanel} from './BalancePanel/BalancePanel';
import {CrossRatesPanel} from './CrossRatesPanel/CrossRatesPanel';
import {FinanceMetrics} from './FinanceMetrics/FinanceMetrics';

export const FinanceBalances = () => (
    <>
      <PageHeading
          tabs={[
            {title: 'Баланс', to: '/finance-balance'},
            {title: 'Переводы', to: '/finance-requests'},
          ]}
          title="Финансы"
      />
      <div className={styles.balances}>
        <VStack $alignItems={'flex-start'} $gap={4}>
          <BalancePanel/>
          <CrossRatesPanel/>
        </VStack>
        <FinanceMetrics/>
      </div>
    </>
)