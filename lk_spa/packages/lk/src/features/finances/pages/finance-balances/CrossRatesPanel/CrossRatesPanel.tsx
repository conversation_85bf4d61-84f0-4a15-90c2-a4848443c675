import {useState} from 'react';
import {getCrossRates} from '@/__old/actions/finances';
import type {CrossRatesResponse} from '@/__old/types/finances';
import {Error} from '@funfarm/kit';
import CopyToClipboard from '@funfarm/kit/CopyToClickboard/CopyToClockboard';
import {useQuery} from '@tanstack/react-query';
import styles from './CrossRatesPanel.module.scss';
import {FinanceBlock} from "@/features/finances/components/FinanceBlock/FinanceBlock";
import {CrossRatesSettings} from "@/__old/components/Finances/Balance/CrossRatesSettings";
import {Box, Button, Dialog, HStack, Skeleton, Text, VStack} from "@/shared/components";

export const CrossRatesPanel = () => {
    const defaultCurrencies = ['RUB', 'EUR', 'CNY'];
    const [top, setTop] = useState<string[]>([...defaultCurrencies]);

    const {data, isLoading, isError} = useQuery<CrossRatesResponse>({
        queryKey: ['finances', 'crossRates'],
        queryFn: () => getCrossRates(),
    });

    const [currencyToChange, setCurrencyToChange] = useState<boolean>(false);

    return (
        <FinanceBlock className={styles.container}>
            <HStack $w={"100%"} $justifyContent={"flex-start"}>
                <Box $flex={1}>
                    <Text $m={0} >Курс к доллару</Text>
                </Box>
                <Button
                    className={styles.crossrateEditIcon}
                    $variant={"filled"}
                    $icon={"pencil"}
                    onClick={() => setCurrencyToChange(true)}
                    style={{width: 36}}
                />
            </HStack>
            <VStack $w={"100%"}>
                {isLoading ? (
                    <Skeleton/>
                ) : isError || !data?.currencies ? (
                    <Error message="Ошибка получения данных"/>
                ) : (
                    <VStack $w={"100%"}>
                        {data.currencies
                            .filter((i) => i.symbol !== '¤' && i.symbol !== '=')
                            .filter((item) => top.includes(item.abrname))
                            .sort((a, b) => {
                                return top.indexOf(a.abrname) - top.indexOf(b.abrname);
                            })
                            .map((currency) => (
                                <div className={styles.crossrateCurrent}
                                     key={currency.id}>
                                    <div>
                                        {currency.name}, {currency.symbol}
                                    </div>
                                    <div className={styles.crossrateCopy}>
                                        <CopyToClipboard>
                                            <div>{Number(currency.ratio).toFixed(4)}</div>
                                        </CopyToClipboard>
                                    </div>
                                </div>
                            ))}
                        <Dialog

                            title="Настройки отображения валют за $ 1"
                            open={currencyToChange}
                            onOpenChange={() => setCurrencyToChange(false)}
                            content={<CrossRatesSettings
                                allCurrencies={data.currencies}
                                currentCurrencies={top}
                                defaultCurrencies={defaultCurrencies}
                                handleCurrencies={setTop}
                            />}
                        >

                        </Dialog>
                    </VStack>
                )}
            </VStack>
        </FinanceBlock>
    );
};
