.finances-grid {
  @include gridCells(12);
  gap: px2rem(24px);

  @include tablet {
    @include gridCells(6);
  }

  @include phone {
    @include gridCells(4);
  }
}

h1,
h2 {
  margin: 0;
}

h1 {
  font-size: px2rem(40px);
}

.finances-content {
  @include colSpan(8);

  @include largeDesktop {
    @include colSpan(7);
  }

  @include tablet {
    @include colSpan(12);
    &:nth-child(8) {
      grid-column-start: 1;
      grid-column-end: 4;
    }
  }

  @include phone {
    @include colSpan(4);

    &:nth-child(8) {
      grid-column-start: 1;
      grid-column-end: 5;
    }
  }

  section {
    margin-bottom: px2rem(22px);
  }

  h2 {
    font-size: $font-size-xxxlarge;
  }
}

.metrics-container {
  @include gridCells(12);
  margin-bottom: px2rem(40px);
}

.balance-panel {
  @include colSpan(4);

  @include largeDesktop {
    @include colSpan(5);
  }

  @include tablet {
    @include colSpan(12);
    &:nth-child(8) {
      grid-column-start: 1;
      grid-column-end: 4;
    }
  }

  @include phone {
    @include colSpan(4);

    &:nth-child(8) {
      grid-column-start: 1;
      grid-column-end: 5;
    }
  }

  > div {
    border-radius: $border-radius-large;
  }

  h3,
  h4,
  p,
  hr {
    margin: 0;
  }

  h3 {
    font-size: $font-size-xlarge;
  }
  p {
    color: $fg-soft;
  }

  .topUp {
    flex-direction: row-reverse;
    align-items: center;
    justify-content: flex-start;
    gap: 2px;
    width: auto;
    height: fit-content;
    border-radius: $border-radius-large;

    div:has(svg) {
      margin-right: 0;
    }
  }

  .balanceValue {
    //@include padding(2, 3);
    width: px2rem(112px);
    border: 1px solid $border-muted;
    border-radius: $border-radius;
    margin-bottom: 0 !important;

    @include megaLargeDesktop {
      width: px2rem(180px);
    }

    span {
      color: $color-input-placeholder;
      cursor: pointer;
    }
  }

  hr {
    width: 100%;
    border: none;
    border-top: 1px solid $grey-900;
  }

  .crossRates {
    color: $grey-500;

    > div {
      padding: 0 8px;
      cursor: pointer;
      &:not(:first-child) {
        border-left: 1px solid $grey-900;
      }
    }

    button {
      padding: 0;
      svg {
        fill: $fg-soft;
      }
    }
  }
}
.balance-history-header {
  font-size: $font-size-base;

  > div {
    text-wrap: nowrap;
    padding: 6px 16px !important;
  }

  > div:not(:last-child) {
    border-right: 1px solid $grey-900;
  }
}

.balance-history-row {
  font-size: $font-size-base;
  border-top: 2px solid $grey-990;

  > div {
    color: $grey-400;
    padding: 9px 16px !important;
    line-height: px2rem(20px);
    text-wrap: nowrap;

    &:has(button) {
      display: flex;
      justify-content: flex-start;
      gap: px2rem(12px);
      align-items: center;
      text-wrap: nowrap;

      button:not(.text-btn) {
        margin: 0;
        padding: 0 !important;
      }
    }
  }

  &.myRequest {
    background-color: $grey-900;
  }
}

.hiddenPlus {
  visibility: hidden;
}
.deleted {
  display: none;
}

.balance-history-table {
  margin-top: 1.5rem;
}

.red {
  color: $red;
}

.green {
  color: $green;
}

.transfers-container {
  @include gridCells(12);
  height: 90%;

  .transfers-total {
    @include colSpan(2);
  }
  .transfers-table {
    @include colSpan(10);
    gap: px2rem(16px);
  }
}
.transfers-filter {
  margin-bottom: px2rem(16px);
  border-radius: $border-radius-large;
}

.close-package-form {
  label {
    font-size: $font-size-base;
    line-height: px2rem(20px);
    font-weight: 400 !important;
    color: $grey-400 !important;
  }

  div:has(> label) {
    margin-bottom: px2rem(16px) !important;
  }

  p {
    margin: px2rem(4px) 0 0 !important;
    color: $grey-400 !important;
  }
}

.formButtons {
  justify-content: flex-end;
}

.metrics-skeleton {
  height: px2rem(144px);
}

.tableSkeleton {
  width: 100%;
  height: px2rem(50px);
}

.date-range-input {
  min-width: px2rem(220px);
}

.red-metrics {
  border: none;
  background-color: $red-blur-2 !important;
  color: $red-500 !important;
  & > p {
    color: $red-500 !important;
  }
}

// input:disabled {
//   background-color: $grey-900 !important;
//   color: $grey-600 !important;
//   border: none !important;
// }

div:has(> input[type='text']) {
  background-color: $grey-700 !important;
}
// div:has(> input:disabled) {
//   border: none !important;
// }

input {
  margin-bottom: 0;
}
.dropzone {
  margin-bottom: px2rem(28px);

  .uploaderArea {
    display: flex;
    align-items: center;
    justify-content: center;
    height: px2rem(100px);
    border: 2px dashed $grey-700;
    border-radius: $border-radius;
    color: $grey-500;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: $grey-500;
      color: $grey-400;
    }
  }

  .previewContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .preview {
      max-width: 100%;
      max-height: px2rem(200px);
      border-radius: $border-radius;
      margin-bottom: px2rem(8px);
    }

    .changeButton {
      padding: px2rem(6px) px2rem(12px);
      background-color: $grey-800;
      border: none;
      border-radius: $border-radius;
      color: $grey-300;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: $grey-700;
      }
    }
  }

  .uploadingMessage {
    margin-top: px2rem(8px);
    color: $grey-400;
    font-style: italic;
    text-align: center;
  }

  .successMessage {
    margin-top: px2rem(8px);
    color: $green;
    text-align: center;
  }
}
.currencyInner {
  margin-left: 7px;
}
div:has(> .currencyInnerBalance) {
  background-color: transparent !important;
  border-color: $grey-950 !important;

  .currencyInnerBalance {
    display: flex;
    align-items: center;
    gap: 4px;
    color: $grey-400 !important;
  }

  svg {
    margin-right: 2px;
  }
  .checkmark-wrapper {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }
  .checkmark-wrapper.visible {
    opacity: 1;
  }
  .loadingWrapper {
    display: inline;
  }
  .loading {
    font-size: 4px;
  }
}

h2 {
  margin: 0;
}
.crossrateDialog {
  overflow-x: hidden;
}
.crossrateDesk {
  color: $grey-400;
  margin-top: 0;
}
.crossrateBtn {
  overflow-x: hidden;
  display: flex;
  justify-content: space-around !important;
}
.crossrateLine {
  width: 110%;
  height: 2px;
  background-color: $grey-900;
  margin-left: -21px;
  margin-top: 30px;
  margin-bottom: 20px;
}
.crossrateItem {
  padding-bottom: 5px;
  font-size: $font-size-xlarge;
}
.crossrateEditIcon {
  padding: 2px 8px !important;
}
.crossrateCurrent {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.crossrateCopy {
  display: flex;
  gap: 32px;
}
.copyableBlock {
  & > div {
    padding: 0 6px;

    &:hover {
      background-color: $blue-gray-800 !important;
    }
  }
}

.accountSelectBox {
  max-width: 12rem;
}
.container {
  display: flex;
  width: 460px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
}
