import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { BalanceHistoryItem, useGetUsedRooms } from '@/features/finances/api';
import { applyCurrency } from '@/shared/utils/currency';

export const useBalanceHistoryColumns = ({ enabled }: { enabled: boolean }) => {
  const { data: roomsData, isLoading, isError } = useGetUsedRooms({ enabled });

  const columnDefs: ColDef[] = [
    {
      headerName: 'Дата',
      field: 'date',
      sortable: false,
    },
    {
      headerName: 'Профит',
      field: 'profit',
      sortable: false,
      valueGetter: (p: ValueGetterParams) => applyCurrency({ amount: p.data.profit, currency: 'USD' }),
      // renderCell: (item: BalanceHistoryItem) => (
      //   <>
      //     {money(item.profit, 'USD')}
      //     {item.editable && (
      //       <Button
      //         view="light"
      //         color="secondary"
      //         size="small"
      //         icon={<IconOn size="large" className={Math.random() > 0.5 ? css.red : css.green} />}
      //       />
      //     )}
      //   </>
      // ),
    },
    ...(roomsData
      ? roomsData.rooms.map((room) => ({
          headerName: room.title,
          field: `room-${room.id}`,
          sortable: false,
          valueGetter: (p: ValueGetterParams) => {
            const { room_balances } = p.data as BalanceHistoryItem;
            const roomBalance = room_balances.find(({ room_id }) => room_id === room.id);
            if (!roomBalance) return '-';
            return applyCurrency({ amount: roomBalance.balance_usd, currency: 'USD' });
          },
          // renderCell: ({ room_balances }: BalanceHistoryItem) => {
          //   const roomBalance = room_balances.find(({ room_id }) => room_id === room.id);
          //   if (!roomBalance) return <>-</>;
          //   return (
          //     <>
          //       {/*JSON.stringify(room_balances)*/}
          //       {money(roomBalance.balance_usd, 'USD')}
          //       {(roomBalance.allow_to_deactivate || roomBalance.allow_to_activate) && (
          //         <Button
          //           view="light"
          //           color="secondary"
          //           size="small"
          //           icon={
          //             <IconOn size="large" className={roomBalance.allow_to_deactivate ? css.red : css.green} />
          //           }
          //         />
          //       )}
          //     </>
          //   );
          // },
        }))
      : []),
    {
      headerName: 'Итого',
      field: 'total',
      sortable: false,
      valueGetter: (p: ValueGetterParams) => applyCurrency({ amount: p.data.total, currency: 'USD' }),
    },
  ];

  return { columnDefs, isLoading, isError };
};
