import {useState} from 'react';
import clsx from 'clsx';
import {changeBalance} from '@/__old/actions/finances';
import {formatDateString} from '@/__old/helpers/formatDates';
import {useBalancesAndBrm} from '@/__old/hooks/useBalancesAndBrm';
import type {BalanceItem} from '@/__old/types/finances';
import {Badge, IconChecked, type IError, Loader} from '@funfarm/kit';
import IconClock from '@funfarm/kit/Icon/icons/Clock';
import styles from './BalanceLine.module.scss';
import {Button, HStack, Input, Text, VStack} from "@/shared/components";

type Props = {
    item: BalanceItem;
    openChangeBalance: (item: BalanceItem) => void;
    deleteBalance?: (item: BalanceItem) => void;
};

export const BalanceLine = ({item, openChangeBalance, deleteBalance}: Props) => {
    const [loading, setLoading] = useState(false);
    const [showCheckmark, setShowCheckmark] = useState(false);
    const [previous, setPrevious] = useState(item.balance_curr);

    const {refetch} = useBalancesAndBrm();

    return (
        <HStack $justifyContent={"space-between"} $w={"100%"}>
            <VStack $alignItems={"flex-start"} $gap={0} $flex={1}>
                <Text $size={14}  $m={0} $color={"fg-default"}>{item.room_title}</Text>
                <Text $size={12}  $m={0} $color={"fg-soft-color"}>{formatDateString(item.date_change)}</Text>
            </VStack>
            <HStack $gap={1}>
                {!!item.requests_amount && (
                    <Badge
                        className={styles.topUp}
                        label={'+' + item.requests_amount}
                        color="orange"
                        size="small"
                        icon={<IconClock fill="success" size="xxxlarge"/>}
                    />
                )}
                <Input
                    value={String(item.balance_curr)}
                    className={styles.balanceValue}
                    rightIcon={
                        <span className={styles.currencyInnerBalance}>
              <span
                  className={clsx(styles.checkmarkWrapper, (showCheckmark || loading) && styles.visible)}>
                {loading ? (
                    <span className={styles.loadingWrapper}>
                    <Loader className={styles.loading}/>
                  </span>
                ) : (
                    showCheckmark && <IconChecked size="large" fill="green"/>
                )}
              </span>
                            {item.currency_symbol}
            </span>
                    }
                    onBlur={(e) => {
                        if (Number(e.target.value) === previous) return;
                        setLoading(true);
                        const newValue = e.target.value.includes(',')
                            ? e.target.value.replace(',', '.')
                            : e.target.value;
                        changeBalance(item.room_id, newValue)
                            .then(() => {
                                refetch();
                                setShowCheckmark(true);
                                setPrevious(Number(newValue));
                                setInterval(() => setShowCheckmark(false), 3000);
                            })
                            .catch((error: IError) => {
                                console.error('Error while updating balance: ', error);
                            })
                            .finally(() => setLoading(false));
                    }}
                />
                {deleteBalance && item.deletable_to_way ? (
                    <Button
                        $variant={"filled"}
                        onClick={() => deleteBalance(item)}
                        className={styles.buttonAction}
                        $icon={"trash"}
                    />
                ) : (
                    <Button
                        $variant={"filled"}
                        onClick={() => openChangeBalance(item)}
                        className={!item.allow_money_requests ? styles.hiddenPlus : styles.buttonAction}
                        $icon={"plus"}
                    />
                )}
            </HStack>
        </HStack>
    );
};
