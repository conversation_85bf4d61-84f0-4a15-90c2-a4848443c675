import { useTranslation } from 'react-i18next';
import { formatDate } from 'date-fns';
import { ColorScheme } from 'styled-components';
import { useGetCurrentPackage } from '@/features/finances/api';
import { Skeleton, Text } from '@/shared/components';
import { HStack } from '@/shared/components/box';
import { Tag } from '@/shared/components/tag';
import { applyCurrency } from '@/shared/utils/currency';

export const CurrentPackageInfo = () => {
  const { t } = useTranslation();

  const { data, isLoading, isError } = useGetCurrentPackage();

  const status = data?.package.status;

  const getColor = (): ColorScheme => {
    if (status === 'open') return 'green';
    if (status === 'hold') return 'accent';
    if (status === 'close') return 'red';
    return 'neutral';
  };

  const getLabel = () => {
    if (status === 'open') return t('finances.packageOpened');
    if (status === 'hold') return t('finances.packageOnCheckout');
    if (status === 'close') return t('finances.packageClosed');
    return status;
  };

  if (isLoading) return <Skeleton $h={7} />;
  if (isError) return null;

  return (
    <div>
      <HStack $justifyContent="start">
        <Tag $colorScheme={getColor()}>{getLabel()}</Tag>
        <Tag $colorScheme="neutral">
          <Text as="span" $plain $color="gray-200">
            {t('global.startDate')}
          </Text>
          <span>&nbsp;</span>
          <span>{formatDate(data?.package.datestart ?? new Date(), 'yyyy-MM-dd HH:mm')}</span>
        </Tag>
        <Tag $colorScheme="neutral">
          <Text as="span" $plain $color="gray-200">
            {t('finances.packageProfit')}
          </Text>
          <span>&nbsp;</span>
          <span>{applyCurrency({ amount: data?.profit.profit ?? 0, currency: 'USD' })}</span>
        </Tag>
      </HStack>
    </div>
  );
};
