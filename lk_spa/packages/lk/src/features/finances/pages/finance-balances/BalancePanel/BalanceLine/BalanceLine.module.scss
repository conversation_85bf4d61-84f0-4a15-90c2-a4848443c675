.balanceValue{
  width: 112px;
  background: var(--bg-surface-2);
}

.hiddenPlus {
  visibility: hidden;
  width: 36px;
}

.button-action{
  width: 36px;
}

div:has(> .currencyInnerBalance) {
  background-color: transparent !important;
  border-color: $grey-950 !important;

  .currencyInnerBalance {
    display: flex;
    align-items: center;
    gap: 4px;
    color: $grey-400 !important;
  }

  svg {
    margin-right: 2px;
  }

  .checkmark-wrapper {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }

  .checkmark-wrapper.visible {
    opacity: 1;
  }

  .loadingWrapper {
    display: inline;
  }

  .loading {
    font-size: 4px;
  }
}

hr {
  width: 100%;
  border: none;
  border-top: 1px solid $grey-900;
}
