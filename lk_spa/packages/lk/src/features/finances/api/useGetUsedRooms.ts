import {ApiRequest} from '@/shared/api';
import {useQuery} from '@tanstack/react-query';

type UsedRoom = {
  id: number;
  title: string;
  allowbalance: boolean;
  allowmoney: boolean;
  allowtourney: boolean;
  allowtransfer: boolean;
  allowwithdraw: boolean;
};

export const useGetUsedRooms = ({enabled}: { enabled?: boolean }) => useQuery({
  queryKey: ['finances', 'rooms', 'used'],
  queryFn: () =>
      new ApiRequest<{ rooms: UsedRoom[] }>({
        method: 'GET',
        url: '/api/check/balances/used-rooms',
      }),
  enabled,
});
