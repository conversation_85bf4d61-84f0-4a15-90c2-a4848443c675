import {ApiRequest} from '@/shared/api';
import {useQuery} from '@tanstack/react-query';

type CurrentPackage = {
  package: {
    id: number;
    datestart: string;
    dateend: string;
    status: string;
    profit: string;
    percprofit: number;
    allowed_to_close: number;
  };
  profit: {
    transaction: number;
    withdraw: number;
    float: number;
    start_balance: number;
    end_balance: number;
    diff_balance: number;
    profit: number;
  };
};

export const useGetCurrentPackage = () => useQuery({
  queryKey: ['finances', 'current-package'],
  queryFn: () =>
      new ApiRequest<CurrentPackage>({
        method: 'GET',
        url: '/api/check/calculations/current',
      }),
});
