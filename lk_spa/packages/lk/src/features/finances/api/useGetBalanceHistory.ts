import {ApiRequest} from '@/shared/api';
import {useQuery} from '@tanstack/react-query';

type RoomBalance = {
  allow_to_activate: number;
  allow_to_deactivate: number;
  balance: number;
  balance_status: string | number | null;
  balance_usd: number;
  changes_count: number;
  last_change_time: string | number | null;
  room_id: number;
};

export type BalanceHistoryItem = {
  date: string;
  editable: boolean;
  profit: number;
  room_balances: RoomBalance[];
  total: number;
};

export const useGetBalanceHistory = ({enabled, pagination}: {
  enabled?: boolean;
  pagination?: { take?: number; skip?: number }
}) => (
    useQuery({
      queryKey: ['finances', 'balance-history', `balance-history-${pagination?.skip}-${pagination?.take}`],
      queryFn: () =>
          new ApiRequest<{
            balances: BalanceHistoryItem[];
            daysCount: number;
          }>({
            method: 'GET',
            url: '/api/check/balances/history',
            params: pagination,
          }),
      enabled,
    }));
