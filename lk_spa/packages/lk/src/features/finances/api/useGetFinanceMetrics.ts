import {ApiRequest} from '@/shared/api';
import {useQuery} from '@tanstack/react-query';

type FinanceMetrics = {
  abi: number;
  mttday: number;
  percbuyin: number;
  percprofit: number;
  need: number;
  needuser: number;
  balance: number;
  moneyproject: number;
  overage: number;
  wmoney: number;
  dvijpl: number;
  diff: number;
  profit: number;
  rang: number;
  roomcount: number;
  datepack: string;
  timepack: number;
  mttpack: number;
  averload: number;
  bonus: number;
  conditionid: number;
  daycountpack: number;
  afterprofit: number;
  blocked_balance: number;
  username: string;
};

export const useGetFinanceMetrics = () => useQuery({
  queryKey: ['finances', 'metrics'],
  queryFn: () =>
      new ApiRequest<{
        data: FinanceMetrics;
        keb_30d: number | null;
        keb_pct: number | null;
        keb_30d_avg_by_league: number | null;
      }>({
        method: 'GET',
        url: '/api/check/finances/brm',
      }),
});
