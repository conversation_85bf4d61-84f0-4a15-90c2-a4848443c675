import type {ReactElement} from 'react';
import clsx from 'clsx';
import {money} from '@funfarm/lk/src/__old/helpers/numbers';
import styles from './Metrics.module.scss';
import {FinanceBlock} from '@/features/finances/components/FinanceBlock/FinanceBlock';
import {Skeleton, Text} from '@/shared/components';

export type MetricsProps = {
  caption: string;
  value?: number | string;
  percentage?: boolean;
  currency?: string;
  isLoading?: boolean;
  className?: string;
  button?: ReactElement;
};

export const Metrics = ({className, button, ...rest}: MetricsProps) => (
    <FinanceBlock header={renderValue(rest)} className={clsx(styles.metrics, className)} helper={button}>
      <Text $m={0} $color="fg-soft-color" $size={16}>{rest.caption}</Text>
    </FinanceBlock>
);

const renderValue = (props: MetricsProps) => {
  if (props.isLoading) return <Skeleton/>;
  if (props.value === undefined || props.value === '-') return '-';
  if (props.percentage) return props.value + '%';
  if (props.currency && props.value) return money(props.value, props.currency);
  return String(props.value);
};
