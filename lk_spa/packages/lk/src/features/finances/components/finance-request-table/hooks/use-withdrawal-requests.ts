import {useQuery} from "@tanstack/react-query";
import {getWithdrawalRequests} from "@/__old/actions/finances";
import type {WithdrawalRequestsFilter} from "@/__old/types/finances";

export const useWithdrawalRequests = (page:number,pageSize:number,filter?: WithdrawalRequestsFilter,) => {
    return useQuery({
        queryKey: ["finances/requests", filter,page],
        queryFn: () => getWithdrawalRequests(filter,(page - 1) * pageSize, pageSize + 1),
    });
};
