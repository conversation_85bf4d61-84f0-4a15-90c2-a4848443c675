import React from "react";
import {Button} from "@/shared/components";
import type {WithdrawalRequest} from "@/__old/types/finances";
import styles from "@/__old/components/Finances/finances.module.scss";

type Props = {
    data: WithdrawalRequest;
    currentUsername?: string;
    onDelete: (id: number) => void;
    onSendMoney: (request: WithdrawalRequest) => void;
    isDeleting: boolean;
};

export const ActionsRenderer = React.memo(({ 
    data, 
    currentUsername, 
    onDelete, 
    onSendMoney, 
    isDeleting 
}: Props) => {
    const isOwnRequest = data.user.name === currentUsername;
    
    if (isOwnRequest) {
        return (
            <Button
                $variant="outline"
                className={styles.textBtn}
                onClick={() => onDelete(data.id)}
                loading={isDeleting}
            >
                Удалить
            </Button>
        );
    }

    return (
        <Button
            $variant="outline"
            className={styles.textBtn}
            onClick={() => onSendMoney(data)}
        >
            Отправить ${data.currency.abrname}
        </Button>
    );
});
