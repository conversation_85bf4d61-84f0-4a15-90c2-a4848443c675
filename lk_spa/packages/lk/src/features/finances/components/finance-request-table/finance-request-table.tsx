import {WithdrawalsFilter} from '@/features/finances/pages/finance-requests/WithdrawalsFilter/WithdrawalsFilter'
import {useState, useMemo, useCallback, useEffect, memo} from 'react';
import {useGlobalState} from "@funfarm/kit";
import {AgGridReact} from "ag-grid-react";
import {useBalancesAndBrm} from "@/__old/hooks/useBalancesAndBrm";
import {useTableTheme} from "@/theme/ag-grid";
import type {WithdrawalRequest, WithdrawalRequestsFilter} from "@/__old/types/finances";
import {
    useDeleteRequest,
    useWithdrawalRequests,
    useColumnDefs,
    useRowClassGetter
} from "./hooks";
import {IUser} from "@/__old/types";
import Pagination from "@funfarm/kit/Pagination/Pagination";
import {usePagination} from "@/__old/hooks/usePagination";
import columnTypes from "@funfarm/kit/DataGrid/dataGrid.columnTypes";

type Props = {
    setSendMoneyRequest: (value: WithdrawalRequest) => void;
};

export const FinanceRequestTable = memo(({setSendMoneyRequest}: Props) => {
    const [currentUser] = useGlobalState<IUser>('user');
    const {refetch} = useBalancesAndBrm();
    const [filter, setFilter] = useState<WithdrawalRequestsFilter>();
    const { page, setPage, pageSize } = usePagination();

    const { deleteRequest, requestDeleting, deleted } = useDeleteRequest(refetch);
    
    const { data, isLoading } = useWithdrawalRequests(page,pageSize,filter);
    
    // Мемоизируем обработчик setSendMoneyRequest
    const handleSendMoneyRequest = useCallback((request: WithdrawalRequest) => {
        setSendMoneyRequest(request);
    }, [setSendMoneyRequest]);
    
    const columnDefs = useColumnDefs(
        currentUser?.username, 
        deleteRequest, 
        requestDeleting, 
        handleSendMoneyRequest
    );
    const getRowClass = useRowClassGetter(currentUser?.username, deleted);
    const theme = useTableTheme();

    // Мемоизируем объект autoSizeStrategy
    const autoSizeStrategy = useMemo(() => ({
        type: 'fitGridWidth' as const,
    }), []);

    // Мемоизируем обработчик setFilter
    const handleFilterChange = useCallback((newFilter: WithdrawalRequestsFilter | undefined) => {
        setFilter(newFilter);
    }, []);

    useEffect(() => {
        console.log(filter,"filter")

    }, [filter]);

    return (
        <>
            <WithdrawalsFilter setValues={handleFilterChange} values={filter}/>
            <AgGridReact
                loading={isLoading}
                rowData={data}
                columnDefs={columnDefs}
                columnTypes={columnTypes}
                suppressDragLeaveHidesColumns
                autoSizeStrategy={autoSizeStrategy}
                theme={theme}
                getRowClass={getRowClass}
                suppressRowHoverHighlight
                domLayout="autoHeight"
            />
            <Pagination
                page={page}
                setPage={setPage}
                loading={isLoading}
                hasNextPage={!isLoading && data?.length ? data?.length > pageSize : false}
            />
        </>
    );
});