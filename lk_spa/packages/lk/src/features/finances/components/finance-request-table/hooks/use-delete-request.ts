import {useCallback, useState} from "react";
import {Toast} from "@funfarm/kit";
import {deleteWithdrawalRequest} from "@/__old/actions/finances";

export const useDeleteRequest = (refetch: () => void) => {
    const [requestDeleting, setRequestDeleting] = useState<number>();
    const [deleted, setDeleted] = useState<number[]>([]);

    const deleteRequest = useCallback(
        async (id: number) => {
            try {
                setRequestDeleting(id);
                await deleteWithdrawalRequest(id);
                setDeleted((prev) => [...prev, id]);
                Toast.success({message: 'Запрос удалён'});
                refetch();
            } catch (error) {
                Toast.error({message: 'Ошибка при удалении запроса'});
            } finally {
                setRequestDeleting(undefined);
            }
        },
        [refetch],
    );

    return { deleteRequest, requestDeleting, deleted };
};
