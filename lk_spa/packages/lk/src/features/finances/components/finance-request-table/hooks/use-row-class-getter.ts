import {useCallback} from "react";
import styles from "@/__old/components/Finances/finances.module.scss";

export const useRowClassGetter = (currentUsername: string | undefined, deleted: number[]) => {
    return useCallback((p: any) => {
        if (p.data?.surplus) return styles.red;
        if (p.data && deleted.includes(p.data.id)) return styles.deleted;
        if (p.data?.user.name === currentUsername) return styles.green;
        return "";
    }, [currentUsername, deleted]);
};
