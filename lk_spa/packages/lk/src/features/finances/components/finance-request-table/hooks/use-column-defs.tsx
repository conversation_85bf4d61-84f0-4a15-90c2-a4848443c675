import {useMemo} from "react";
import type {ColDef, ICellRendererParams, ValueFormatterParams} from "ag-grid-community";
import type {WithdrawalRequest} from "@/__old/types/finances";
import {money} from "@/shared/utils/helpers";
import {AccountDataRenderer} from "../components/account-data-renderer";
import {ActionsRenderer} from "../components/actions-renderer";

export const useColumnDefs = (
    currentUsername: string | undefined,
    deleteRequest: (id: number) => void,
    requestDeleting: number | undefined,
    setSendMoneyRequest: (value: WithdrawalRequest) => void
) => {
    return useMemo<ColDef<WithdrawalRequest>[]>(
        () => [
            {field: 'datestart', headerName: 'Дата', type: 'date'},
            {field: 'room_name', headerName: 'Счёт'},
            {
                field: 'toname',
                headerName: 'Данные счёта',
                flex: 1,
                cellRenderer: (p: ICellRendererParams) => (
                    <AccountDataRenderer data={p.data} />
                ),
            },
            {
                field: 'amountcur',
                headerName: 'Сумма',
                valueFormatter: (p: ValueFormatterParams) =>
                    p.data.surplus
                        ? 'Для излишков'
                        : !p.value
                            ? '-'
                            : money(Number(p.value), p.data.currency.abrname),
            },
            {
                cellRenderer: (p: ICellRendererParams) => (
                    <ActionsRenderer
                        data={p.data}
                        currentUsername={currentUsername}
                        onDelete={deleteRequest}
                        onSendMoney={setSendMoneyRequest}
                        isDeleting={p.data.id === requestDeleting}
                    />
                ),
            },
        ],
        [currentUsername, deleteRequest, requestDeleting, setSendMoneyRequest],
    );
};
