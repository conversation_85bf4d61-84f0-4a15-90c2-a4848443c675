import React from "react";
import {Row} from "@funfarm/kit";
import CopyToClipboard from "@funfarm/kit/CopyToClickboard/CopyToClockboard";
import type {WithdrawalRequest} from "@/__old/types/finances";
import styles from "@/__old/components/Finances/finances.module.scss";

type Props = {
    data: WithdrawalRequest;
};

export const AccountDataRenderer = React.memo(({ data }: Props) => (
    <Row className={styles.copyableBlock}>
        {!!data.account?.useraccount && (
            <CopyToClipboard>{data.account.useraccount}</CopyToClipboard>
        )}
        {!!data.account?.details?.firstName && (
            <CopyToClipboard>
                {data.account.details.firstName}
            </CopyToClipboard>
        )}
        {!!data.account?.details?.lastName && (
            <CopyToClipboard>
                {data.account.details.lastName}
            </CopyToClipboard>
        )}
    </Row>
));
