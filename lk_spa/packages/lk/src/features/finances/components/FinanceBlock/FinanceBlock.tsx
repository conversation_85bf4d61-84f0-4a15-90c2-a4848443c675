import {cloneElement, type CSSProperties, type ReactElement, type ReactNode} from 'react'
import styles from './FinanceBlock.module.scss';
import {Paper} from '@/shared/components';

type Props = {
  children: ReactNode;
  header?: string | ReactElement;
  className?: string;
  style?: CSSProperties;
  helper?: ReactElement;
}

export const FinanceBlock = ({className, header, helper, style, children}: Props) => (
    <Paper className={className} style={style} $p={4}>
      {!!header && (
          <h3 className={styles.header}>
            {header}
            {helper && cloneElement(helper, {className: styles.helper})}
          </h3>
      )}
      {children}
    </Paper>
);
