import styles from '../referal-page.module.scss';
import {Heading, HStack, Paper, VStack, Text} from '@/shared/components';
import {useReferral} from '@/features/referal/api/useReferal';
import {useTranslation} from 'react-i18next';

export const CommonStatistics = () => {
  const {data} = useReferral()
  const {t} = useTranslation();

  return (
      <Paper $display="flex" $flexDirection="column" $justifyContent="center" $alignItems="center" $gap={{base: 12, md: 16}} $h="100%" $p={{base: 12, md: 16}}>
        <Heading $size={{base: 24, md: 32, lg: 40}} className={styles.topContent}>{t('referal.commonStatistic.title')}</Heading>
        <HStack $flexDirection={{base: "column", md: "row"}} $gap={{base: 8, md: 0}} $alignItems="center">
          <VStack $gap={4} $alignItems="center">
            <Text $size={{base: 24, md: 28, lg: 32}} $m={0}>{data?.total_invited}</Text>
            <Text $size={{base: 12, md: 13, lg: 14}} $m={0} $color="fg-soft-color" $textAlign="center">{t('referal.commonStatistic.people_join')}</Text>
          </VStack>
          <div className={styles.divider}/>
          <VStack $gap={4} $alignItems="center">
            <Text $size={{base: 24, md: 28, lg: 32}} $m={0}>{data?.top_inviter_this_month}</Text>
            <Text $size={{base: 12, md: 13, lg: 14}} $m={0} $color="fg-soft-color" $textAlign="center">{t('referal.commonStatistic.most_friend')}</Text>
          </VStack>
        </HStack>
      </Paper>
  )
}