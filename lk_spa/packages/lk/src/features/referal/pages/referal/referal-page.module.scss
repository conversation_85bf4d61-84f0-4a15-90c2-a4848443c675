.top-content {
  text-align: center;
  z-index: 2;
}

.link-box {
  z-index: 2;
  width: 100%;
  display: flex;
  padding: 16px;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  background: var(--bg-surface-2, #232134);
  box-shadow: 0 0 50.6px 0 rgba(161, 131, 196, 0.50), 0 0 17px 0 rgba(208, 169, 255, 0.50);

  @media (min-width: 768px) {
    width: 600px;
    padding: 24px;
  }
}

.radial-gradient {
  position: absolute;
  top: 120px;
  width: 1089px;
  height: 1089px;
  flex-shrink: 0;
  border-radius: 1089px;
  opacity: 0.7;
  background: radial-gradient(50% 50% at 50% 50%, var(--tournir-pallete2-purple-start) 0%, var(--tournir-pallete2-purple-end) 100%);
  filter: blur(30.817020416259766px);
}

.divider {
  width: 1px;
  height: 72px;
  background: var(--bg-disabled);
  margin: 0 64px;

  @media (max-width: 767px) {
    width: 80%;
    height: 1px;
    margin: 8px 0;
  }
}

.divider-horizontal {
  width: 101%;
  height: 1px;
  background: var(--border-disabled);
  margin-top: -16px;
  margin-bottom: -16px;

  @media (min-width: 768px) {
    display: none;
  }
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 12px;

  @media (min-width: 768px) {
    flex-direction: row;
    gap: 42px;
    padding: 0 20px;
  }

  @media (min-width: 1440px) {
    padding: 0 118px;
  }

  .step-item {
    display: flex;
    padding: 16px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 12px;
    flex: 1 0 0;
    border-radius: 12px;
    background: var(--bg-surface-2, #232134);

    @media (min-width: 768px) {
      padding: 24px;
      gap: 16px;
    }
  }
}

.button-gradient {
  background: linear-gradient(110deg, #C266D6 2.34%, #8849D1 74.46%), var(--accent-default, #8849D1);
}

.decorImage {
  position: absolute;
  bottom: 0;
  height: 753px;
}
