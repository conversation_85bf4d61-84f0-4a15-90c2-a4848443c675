import {
    Box,
    Dialog,
    Grid,
    GridItem,
    Heading,
    HStack,
    Icon,
    Paper,
    useToast,
    VStack,
    Text,
    Skeleton
} from '@/shared/components';
import styles from '../referal-page.module.scss';
import classNames from 'classnames';
// import blockLogo from '/img/referal-main.webp';
import {useProfile} from '@/features/account/api';
import {useTranslation} from 'react-i18next';
import {useBoolean} from '@/shared/hooks';
import {QrModal} from './QrModal/QrModal';

export const LinkBlock = () => {
  const {data: profile} = useProfile();
  const toast = useToast();
  const {t} = useTranslation();
  const [isOpen, openModal, closeModal] = useBoolean();

  const handleCopy = async () => {
    if (profile?.referral_link) {
      try {
        await navigator.clipboard.writeText(profile.referral_link);
        toast.success({title: t('referal.linkBlock.copy')});
      } catch (err) {
        console.error('Failed to copy:', err);
      }
    }
  };

  if (!profile) return <Skeleton $h={44}/>

  return (
      <Paper $p={0} $px={{base: 2, md: 8}}>
        <VStack $h="100%" $overflow="hidden" $p={{base: 12, md: 16}} $position="relative" $justifyContent="flex-start">
          <Heading $size={{base: 32, md: 48, lg: 64}} className={styles.topContent}>{t('referal.linkBlock.title')}</Heading>
          <Text $size={{base: "lg", md: "xl", lg: "2xl"}} $color="fg-soft-color" $mt={{base: 4, md: 6}} $mb={{base: 6, md: 8}} className={styles.topContent}>
              {t('referal.linkBlock.description')}
          </Text>
          <Grid className={classNames(styles.linkBox, styles.topContent)}>
            <GridItem $size={{base: 10, md: 11}}>
              <VStack $alignItems="flex-start" $gap={2}>
                <Text $color="fg-soft-color" $m={0}>{t('referal.linkBlock.link_title')}</Text>
                <Text $size={{base: "sm", md: "base"}} $m={0} $wordBreak="break-all">{profile?.referral_link}</Text>
              </VStack>
            </GridItem>
            <GridItem $size={{base: 2, md: 1}}>
              <HStack $gap={{base: 2, md: 5}} $justifyContent="center">
                <Box $cursor="pointer" onClick={handleCopy}>
                  <Icon name="files-copy"/>
                </Box>
                {/*<Box $cursor={"pointer"} onClick={openModal}>
                    <Icon name={"scan"}/>
                </Box>*/}
              </HStack>
            </GridItem>
          </Grid>
          {/*<div className={styles.radialGradient}/>
          <img src={blockLogo} className={styles.decorImage} alt=""/>*/}
          <Dialog
              open={isOpen}
              onOpenChange={closeModal}
              width="xs"
              content={<QrModal url={profile.referral_link} avatarUrl={profile.avatar} userName={profile.username}/>}
          />
        </VStack>
      </Paper>
  )
}