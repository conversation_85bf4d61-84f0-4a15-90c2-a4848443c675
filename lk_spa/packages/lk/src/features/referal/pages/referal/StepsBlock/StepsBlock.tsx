import {Grid, GridItem, Heading, Paper, Text, VStack} from '@/shared/components';
import {useTranslation} from 'react-i18next';
import styles from '../referal-page.module.scss';

export const StepsBlock = () => {
  const {t} = useTranslation();

  const steps = [
    {title: t('referal.stepsBlock.step_1_title'), description: t('referal.stepsBlock.step_1_description')},
    {title: t('referal.stepsBlock.step_2_title'), description: t('referal.stepsBlock.step_2_description')},
    {title: t('referal.stepsBlock.step_3_title'), description: t('referal.stepsBlock.step_3_description')}
  ]

  return (
      <Paper $mt={2}>
        <VStack $h="100%" $overflow="hidden" $position="relative" $justifyContent="flex-start" $gap={{base: 8, md: 14}} $p={{base: 12, md: 16}}>
          <Heading $size={{base: 24, md: 32, lg: 40}} className={styles.topContent}>{t('referal.stepsBlock.block_title')}</Heading>
          <Grid className={styles.stepsContainer} $spacing={{base: 3, md: 10}}>
            {steps.map((step, i) => (
                <GridItem key={step.title} $size={{base: 12, md: 6, lg: 4}}>
                  <Paper $h="100%" className={styles.stepItem}>
                    <Text $size={{base: 32, md: 42, lg: 54}} $m={0} $textAlign="center" $color="accent">{i + 1}</Text>
                    <Text $size={{base: 16, md: 20, lg: 24}} $m={0} $textAlign="center">{step.title}</Text>
                    <Text $size={{base: 12, md: 13, lg: 14}} $m={0} $textAlign="center" $color="fg-soft-color">{step.description}</Text>
                  </Paper>
                </GridItem>
            ))}
          </Grid>
        </VStack>
      </Paper>
  )
}