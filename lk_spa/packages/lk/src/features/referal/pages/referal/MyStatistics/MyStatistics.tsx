import styles from '../referal-page.module.scss';
import {Heading, HStack, Paper, VStack, Text} from '@/shared/components';
import {useReferral} from '@/features/referal/api/useReferal';
import {useTranslation} from 'react-i18next';

export const MyStatistics = () => {
  const {data} = useReferral()
  const {t} = useTranslation();

  return (
      <Paper $display="flex" $flexDirection="column" $justifyContent="center" $alignItems="center" $gap={{base: 12, md: 16}} $h="100%" $p={{base: 12, md: 16}}>
        <Heading $size={{base: 24, md: 32, lg: 40}} className={styles.topContent}>{t('referal.myStatistic.title')}</Heading>
        <HStack $flexDirection={{base: "column", md: "row"}} $gap={{base: 8, md: 0}} $alignItems="center">
          <VStack $gap={4} $alignItems="center">
            <Text $size={{base: 24, md: 28, lg: 32}} $m={0}>{data?.my_referrals_applied_total}</Text>
            <Text $size={{base: 12, md: 13, lg: 14}} $m={0} $color="fg-soft-color" $textAlign="center">{t('referal.myStatistic.friends_invites')}</Text>
          </VStack>
          <div className={styles.divider}/>
          <VStack $gap={4} $alignItems="center">
            <Text $size={{base: 24, md: 28, lg: 32}} $m={0}>{data?.my_referrals_contracted_total}</Text>
            <Text $size={{base: 12, md: 13, lg: 14}} $m={0} $color="fg-soft-color" $textAlign="center">{t('referal.myStatistic.friends_contract')}</Text>
          </VStack>
          <div className={styles.divider}/>
          <VStack $gap={4} $alignItems="center">
            <Text $size={{base: 24, md: 28, lg: 32}} $m={0}>{data?.payout_sum}</Text>
            <Text $size={{base: 12, md: 13, lg: 14}} $m={0} $color="fg-soft-color" $textAlign="center">{t('referal.myStatistic.money_received')}</Text>
          </VStack>
        </HStack>
      </Paper>
  )
}