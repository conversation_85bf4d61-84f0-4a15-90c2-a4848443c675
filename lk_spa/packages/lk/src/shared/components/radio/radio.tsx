import { forwardRef } from 'react';
import styled from 'styled-components';
import * as RadixUIRadioGroup from '@radix-ui/react-radio-group';
import { BoxStyledProps, HStack, VStack } from '../box';

type RadioGroupProps = {
  direction?: 'row' | 'column';
} & RadixUIRadioGroup.RadioGroupProps &
  BoxStyledProps;

export const RadioGroup = ({ direction = 'row', children, ...props }: RadioGroupProps) => {
  const Component = direction === 'row' ? HStack : VStack;
  return (
    <RadixUIRadioGroup.Root asChild {...props}>
      <Component children={children} />
    </RadixUIRadioGroup.Root>
  );
};

const StyledRadioItem = styled(RadixUIRadioGroup.Item)(({ theme }) => ({
  all: 'unset',

  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',

  borderRadius: theme.radius.full,
  backgroundColor: theme.color['bg-surface-3'],

  border: `1px solid ${theme.color['border-soft']}`,

  width: theme.spacing[5],
  height: theme.spacing[5],

  transition: `all 0.15s ${theme.easing['ease-out-cubic']}`,

  cursor: 'pointer',

  '&[data-state="checked"]': {
    backgroundColor: theme.color['accent'],
    borderColor: theme.color['accent-hover'],
  },

  '&[disabled]': {
    cursor: 'default',
    opacity: 0.64,
  },
}));

export const RadioItem = forwardRef<HTMLButtonElement, RadixUIRadioGroup.RadioGroupItemProps>((props, ref) => {
  return (
    <StyledRadioItem ref={ref} {...props}>
      <RadioIndicator />
    </StyledRadioItem>
  );
});

export const RadioIndicator = styled(RadixUIRadioGroup.Indicator)(({ theme }) => ({
  borderRadius: theme.radius.full,
  backgroundColor: theme.color.white,

  width: theme.spacing[2],
  height: theme.spacing[2],
}));
