import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { Box } from '../box';
import { RadioGroup, RadioItem } from './radio';

const meta = {
  title: 'Components/Radio',
  component: (() => <></>) as typeof RadioGroup,
  argTypes: {
    direction: {
      options: ['row', 'column'],
      control: { type: 'select' },
    },
  },
} satisfies Meta<typeof RadioGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    direction: 'row',
  },
  render: ({ direction }) => (
    <Box $p={4}>
      <RadioGroup direction={direction} defaultValue="1">
        <RadioItem value="1" />
        <RadioItem value="2" />
        <RadioItem value="3" />
        <RadioItem value="4" disabled />
      </RadioGroup>
    </Box>
  ),
};
