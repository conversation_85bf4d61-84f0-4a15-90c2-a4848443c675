import { styled, Interpolation, ColorScheme } from 'styled-components';
import { getColorSchemePalette, hexToRgb } from '@/theme';

interface StyledProps {
  // $size?: 'sm' | 'md';
  $colorScheme?: ColorScheme;
}

const StyledTag = styled.span<StyledProps>(({ theme, $colorScheme = 'neutral' }) => {
  const { main, subtle } = getColorSchemePalette(theme, $colorScheme);
  const styles: Interpolation<StyledProps> = {
    display: 'inline-flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: $colorScheme === 'neutral' ? theme.color['fg-default'] : main,
    fontFamily: theme.fontFamily.primary,
    ...theme.font.sm,
    fontWeight: 500,
    paddingInline: theme.spacing[3],
    backgroundColor: hexToRgb($colorScheme === 'neutral' ? subtle : main, 0.15),
    borderRadius: theme.radius.xs,
    minWidth: theme.spacing[5],
    height: theme.spacing[7],
  };

  // TODO
  // if ($size === 'sm')
  //   Object.assign(styles, {
  //     ...theme.font.sm,
  //     minWidth: theme.spacing[4],
  //     height: theme.spacing[6],
  //     paddingInline: 2,
  //   });

  return styles;
});

export const Tag = StyledTag;
