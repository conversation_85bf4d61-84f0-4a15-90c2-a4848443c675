import type { Meta, StoryObj } from '@storybook/react-vite';
import { Tag } from './tag';

type Props = React.ComponentProps<typeof Tag>;
const colorSchemes: Props['$colorScheme'][] = ['accent', 'neutral', 'red', 'green', 'orange'];

const meta = {
  title: 'Components/Tag',
  component: Tag,
  argTypes: {
    $colorScheme: {
      options: colorSchemes,
      control: { type: 'select' },
    },
  },
} satisfies Meta<typeof Tag>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Tag',
    $colorScheme: 'neutral',
  },
};
