import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { Box } from '../box';
import { Fade } from './fade';

const meta = {
  title: 'Components/Fade',
  component: Fade,
} satisfies Meta<typeof Fade>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    $in: true,
  },
  render: (props) => (
    <Fade {...props}>
      <Box $p={4}>Content</Box>
    </Fade>
  ),
};
