import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { Box } from '../box';
import { Accordion, CompactAccordionItem } from './accordion';

const Component = () => (
  <Accordion type="single">
    <CompactAccordionItem value="1" icon="poker-dom" label="PD ₽" content={<Box $p={4}>Content</Box>} />
  </Accordion>
);

const meta = {
  title: 'Components/Accordion',
  component: Component,
} satisfies Meta<typeof Component>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Compact: Story = {
  render: Component,
};
