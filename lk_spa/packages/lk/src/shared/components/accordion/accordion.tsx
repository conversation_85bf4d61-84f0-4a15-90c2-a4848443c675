import {type BoxStyledProps, Text} from '@/shared/components'
import { keyframes, styled } from 'styled-components';
import * as RadixUIAccordion from '@radix-ui/react-accordion';
import { Box } from '../box';
import { Icon, IconName } from '../icon';

const StyledAccordionRoot = styled(RadixUIAccordion.Root)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing[4],
}));

StyledAccordionRoot.defaultProps = {
  collapsible: true,
};

export const Accordion = StyledAccordionRoot;

const StyledAccordionItem = styled(RadixUIAccordion.Item)(({ theme }) => ({
  backgroundColor: theme.color['bg-surface'],
  borderRadius: theme.radius.lg,
}));

const StyledAccordionHeader = styled(RadixUIAccordion.Header)(({ theme }) => ({
  borderRadius: theme.radius.lg,
}));

const StyledAccordionTrigger = styled(RadixUIAccordion.Trigger)(({ theme }) => ({
  all: 'unset',
  width: '100%',
  display: 'flex',
  justifyContent: 'start',
  alignItems: 'center',
  gap: theme.spacing[2.5],
  padding: theme.spacing[5],

  ...theme.font.lg,
  fontFamily: theme.fontFamily.primary,
  fontWeight: 500,

  color: theme.color['fg-soft-color'],
  cursor: 'pointer',

  '& > svg': {
    transition: 'transform 0.2s ease',
  },

  '&[data-state="open"] > svg[data-cheveron]': {
    transform: 'rotate(180deg)',
  },
}));

const slideDown = keyframes`
  from { height: 0; opacity: 0; }
  to { height: var(--radix-accordion-content-height); opacity: 1; }
`;

const slideUp = keyframes`
  from { height: var(--radix-accordion-content-height); opacity: 1; }
  to { height: 0; opacity: 0; }
`;

const StyledAccordionContent = styled(RadixUIAccordion.Content)`
  ${({ theme }) => ({
    position: 'relative',
    overflow: 'hidden',

    '&:before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: theme.spacing[5],
      width: `calc(100% - ${theme.spacing[10]})`,
      height: '1px',
      backgroundColor: theme.color['border-muted-color'],
    },
  })}

  &[data-state="open"] {
    animation: ${slideDown} 0.35s ${({ theme }) => theme.easing['ease-out-expo']};
  }

  &[data-state='closed'] {
    animation: ${slideUp} 0.35s ${({ theme }) => theme.easing['ease-out-expo']};
  }
`;

// TODO: move to compact-accordion-item.tsx when there will be more variants of styles
type CompactAccordionItemProps = {
  value: string;
  icon?: IconName;
  label: string;
  content: React.ReactNode;
  triggerStyles?: BoxStyledProps;
};

export const CompactAccordionItem = (props: CompactAccordionItemProps) => {
  const {value, icon, label, content,triggerStyles} = props;
  return (
    <StyledAccordionItem value={value}>
      <StyledAccordionHeader>
        <StyledAccordionTrigger>
          {!!icon && <Icon name={icon} $size={24} />}
          <Text $m={0} {...triggerStyles}>{label}</Text>
          <Icon name="cheveron-down" $size={24} data-cheveron />
        </StyledAccordionTrigger>
      </StyledAccordionHeader>
      <StyledAccordionContent>
        <Box $p={5}>{content}</Box>
      </StyledAccordionContent>
    </StyledAccordionItem>
  );
};
