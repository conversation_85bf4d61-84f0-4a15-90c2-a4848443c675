import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { Box } from '../box';
import { DndList } from './dnd-list';
import { DndListItem } from './dnd-list-item';
import { DndItemData, DragEndEvent } from '.';

const Component = () => {
  const [items, setItems] = useState([1, 2, 3]);

  const reorder = (from: number, to: number) => {
    setItems((prev) => {
      const newState = [...prev];
      newState.splice(from, 1);
      newState.splice(to, 0, prev[from]);
      return newState;
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { index: activeItemIndex } = event.active?.data.current as DndItemData;
    const overItemIndex = (event.over?.data.current as DndItemData)?.index;
    /** check drag indexes */
    if (activeItemIndex === undefined || overItemIndex === undefined || activeItemIndex === overItemIndex) return;
    reorder(activeItemIndex, overItemIndex);
  };

  return (
    <DndList onDragEnd={handleDragEnd}>
      {items.map((item, index) => (
        <DndListItem
          key={index}
          item={({ listeners, attributes, setActivatorNodeRef }) => (
            <Box {...listeners} {...attributes} ref={setActivatorNodeRef} $bgColor="accent" $p={4} $mb={2}>
              Item {item}
            </Box>
          )}
          itemId={String(item)}
          itemIndex={index}
          itemData={{ index }}
          listGap={8}
        />
      ))}
    </DndList>
  );
};

const meta = {
  title: 'Components/DndList',
  component: DndList,
} satisfies Meta<typeof DndList>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {} as React.ComponentProps<typeof DndList>,
  render: Component,
};
