import type { Meta, StoryObj } from '@storybook/react-vite';
import { Button } from '../button';
import { Toast } from './toast';
import { ToastStack } from './toast-stack';
import { useToast } from './toast.context';

const Component = () => {
  const toast = useToast();
  const handleClick = () => {
    toast({ title: 'Title' });
  };
  return <Button onClick={handleClick}>Use toast</Button>;
};

const meta = {
  title: 'Components/Toast',
  component: Toast,
} satisfies Meta<typeof Toast>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  render: () => (
    <ToastStack>
      <Component />
    </ToastStack>
  ),
};
