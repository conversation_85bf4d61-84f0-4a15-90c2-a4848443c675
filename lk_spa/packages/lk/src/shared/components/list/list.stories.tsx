import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { ChipList, ChipListItem } from './chip-list';

const meta = {
  title: 'Components/ChipList',
  component: ChipList,
} satisfies Meta<typeof ChipList>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <>
        <ChipListItem>Item 1</ChipListItem>
        <ChipListItem>Item 2</ChipListItem>
        <ChipListItem>Item 3</ChipListItem>
      </>
    ),
  },
};
