.dropdownMenuTrigger {
  cursor: pointer;
  width: 100%;
  display: flex;
  justify-content: space-between;

  &[data-state='open'] {
    border: 1px solid var(--fg-muted) !important;
  }
}

.dropdownMenuContent {
  cursor: pointer;
  min-width: var(--radix-dropdown-menu-trigger-width);
  padding: 8px 4px;
  border-radius: 6px;
  background-color: var(--bg-surface-2);
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.55);
  z-index: 2000;
  animation-duration: 0.6s;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  max-height: 300px;
  overflow-y: auto;

  &[data-side='top'] {
    animation-name: slideUp;
  }

  &[data-side='bottom'] {
    animation-name: slideDown;
  }
}

.dropdownMenuItem {
  font-family: "Inter", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--fg-default);
  padding: 10px 12px;
  user-select: none;
  text-decoration: none;
  outline: none;
  border-radius: 6px;
  transition: all 150ms ease-out;

  &:hover {
    background-color: var(--bg-surface-3);
  }
}

.dropdownCheckboxItem {
  font-family: "Inter", sans-serif;
  font-size: 14px;
  line-height: 1.42857;
  color: var(--fg-default);
  display: flex;
  padding: 8px 12px;
  border-radius: 6px;
  gap: 8px;
  user-select: none;
  text-decoration: none;
  outline: none;
  transition: all 150ms ease-out;

  &:hover {
    background-color: var(--bg-surface-3);
  }

  .checkBox {
    width: 16px;
    height: 18px;

    .check {
      padding: 2px 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.dropdownMenuSeparator {
  height: 1px;
  background-color: var(--fg-muted);
  opacity: 0.3;
  margin: 4px 0;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
