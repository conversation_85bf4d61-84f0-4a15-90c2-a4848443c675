import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { Box } from '../box';
import { Button } from '../button';
import { Dialog } from './dialog';

const meta = {
  title: 'Components/Dialog',
  component: Dialog,
} satisfies Meta<typeof Dialog>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {} as React.ComponentProps<typeof Dialog>,
  render: () => (
    <Dialog title="Dialog title" content={<Box $p={4}>Content</Box>} trigger={<Button>Open</Button>} />
  ),
};
