import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import i18n from '@/__old/helpers/i18n';
import { env } from '@/env';
import styles from './footer.module.scss';

type Props = {
  className?: string;
}

export const Footer = (props: Props) => {
  const { className } = props;

  const { t } = useTranslation();

  const supportedLngs: Record<string, string> = {
    ru: 'RU',
    en: 'EN' /* uk: 'UA'*/,
  };

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
  };

  return (
    <footer className={clsx(styles.container, className)}>
      <div className={styles.copy}>
        <span>&copy; {new Date().getFullYear()} FunFarm</span>
        <span>{env.version}</span>
      </div>
      <nav>
        <a href="#">{t('Privacy policy')}</a>
        {Object.keys(supportedLngs)
          // .filter(lang => lang === currentLang)
          .map((lang) => (
            <span key={lang} onClick={() => changeLanguage(lang)}>
              {supportedLngs[lang]}
            </span>
          ))}
      </nav>
    </footer>
  );
};
