import {Footer} from '@/shared/layout/footer';
import {SignIn} from '@/features/auth/pages/sign-in/sign-in'
import styles from './Auth-Layout.module.scss';

type Props = {
  twoFA?: boolean;
}

export const AuthLayout = ({twoFA}: Props) => (
    <div className={styles.wrapper}>
      <div className={styles.authPageContent}>
        <div className={styles.container}>
          <SignIn twoFA={twoFA}/>
        </div>
        <Footer className={styles.footer}/>
      </div>
    </div>
);
