.header {
  display: flex;
  align-items: center;
  width: 100%;
  @include margin(0, 0, 5);

  &__buttons {
    flex-grow: 1;
    display: flex;
    justify-content: flex-end;
  }
  .tabs {
    display: flex;
    padding: 4px;
    border: 1px solid var(--border-muted-color);
    border-radius: 6px;
    margin: 0 12px;

    .tab {
      font-family: "Inter", sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.42857;
      text-align: center;
      color: var(--fg-soft-color);
      padding: 4px 12px;
      border-radius: 4px;
      transition: all 150ms ease-out;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        background-color: var(--bg-surface-3);
      }

      &.active {
        background-color: var(--bg-surface-3);
      }
    }
  }
}

.button {
  flex-grow: 1;
  display: flex;
  justify-content: flex-end;
  position: relative;

  &__settings {
    position: relative;
  }
}