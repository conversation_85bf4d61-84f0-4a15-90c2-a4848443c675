import type {ReactNode} from 'react'
import { NavLink } from 'react-router-dom';
import styles from './PageHeading.module.scss';

export type Props = {
  title?: string;
  tabs?: {
    to: string;
    title: string;
  }[];
  renderButtons?: () => ReactNode;
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
};

export const PageHeading = ({ title, tabs, renderButtons, tag }: Props) => {
  const Tag = tag || 'h1';

  return (
    <section className={styles.header}>
      {!!title && <Tag>{title}</Tag>}

      {tabs && (
        <div className={styles.tabs}>
          {tabs.map((tab, index) => (
            <NavLink
              key={index}
              to={tab.to}
              end
              className={({ isActive }) =>
                `${styles.tab} ${isActive ? styles.active : ''}`
              }
            >
              {tab.title}
            </NavLink>
          ))}
        </div>
      )}

      {renderButtons && <div className={styles.button}>{renderButtons()}</div>}
    </section>
  );
};
